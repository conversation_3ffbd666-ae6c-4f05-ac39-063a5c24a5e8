import { DiseaseRule } from '@/types';

export const diseaseRules: DiseaseRule[] = [
  {
    id: 'DR001',
    diseaseName: '法布雷病',
    ruleName: '神经性疼痛（肢端皮肤疼痛）',
    ruleDescription: '患者存在肢端疼痛、感觉异常、血管角质瘤、角膜涡状混浊等症状',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: '肢端疼痛|感觉异常|血管角质瘤|角膜涡状混浊',
    nlpScore: 5,
    // 大模型相关配置
    modelRuleMatching: false,
    modelPromptTemplate: '',
    modelScore: 0,
    dataSource: '电子病历',
    dataSourceSql: 'SELECT * FROM medical_records WHERE chief_complaint REGEXP "肢端疼痛|感觉异常"',
    department: '神经内科',
    systemSource: '病历文书',
    部位: '周围神经',
    createdAt: '2025-06-15T09:30:00Z',
    isActive: true,
    weight: 5,
    category: '神经系统',
    reference: 'Yoshi<PERSON> S, et al. Orphanet J Rare Dis. 2020 Aug 26;15(1):220.'
  },
  {
    id: 'DR002',
    diseaseName: '法布雷病',
    ruleName: '皮肤血管角质瘤',
    ruleDescription: '皮肤出现典型的血管角质瘤表现',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: '皮肤血管角质瘤|血管角质瘤',
    nlpScore: 10,
    // 大模型相关配置
    modelRuleMatching: true,
    modelPromptTemplate: '分析以下病历内容，判断患者是否有皮肤血管角质瘤表现: {{病历文本}}。返回1(是)或0(否)。',
    modelScore: 10,
    dataSource: '电子病历',
    dataSourceSql: 'SELECT * FROM medical_records WHERE diagnosis LIKE "%血管角质瘤%"',
    department: '皮肤科',
    systemSource: '病历文书、诊断',
    部位: '皮肤',
    createdAt: '2025-06-15T09:35:00Z',
    isActive: true,
    weight: 10,
    category: '皮肤系统',
    reference: 'Yoshida S, et al. Orphanet J Rare Dis. 2020 Aug 26;15(1):220.'
  },
  {
    id: 'DR003',
    diseaseName: '法布雷病',
    ruleName: '心脏：乳头肌肥大',
    ruleDescription: '超声检查显示乳头肌肥大，乳头肌面积>3.6cm²且乳头肌面积/左心室横截面积比>0.18',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: '乳头肌肥大|乳头肌增厚',
    nlpScore: 4,
    // 大模型相关配置
    modelRuleMatching: true,
    modelPromptTemplate: '根据超声心动图报告，判断是否存在乳头肌肥大: {{报告内容}}。返回1(是)或0(否)。',
    modelScore: 4,
    dataSource: '影像学检查',
    dataSourceSql: 'SELECT * FROM echocardiography WHERE papillary_muscle_area > 3.6',
    department: '心内科',
    systemSource: '心超',
    部位: '心脏',
    createdAt: '2025-06-15T09:40:00Z',
    isActive: true,
    weight: 4,
    category: '心血管系统',
    reference: 'Niemann M, et al. Ultrasound Med Biol. 2011 Jan;37(1):37-43.'
  },
  {
    id: 'DR004',
    diseaseName: '法布雷病',
    ruleName: '心脏磁共振T1 mapping降低',
    ruleDescription: '心脏磁共振检查显示左心室心肌Native T1值降低',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: 'T1 mapping降低|Native T1降低',
    nlpScore: 10,
    // 大模型相关配置
    modelRuleMatching: true,
    modelPromptTemplate: '分析心脏磁共振报告，判断是否存在左心室心肌Native T1值降低: {{报告内容}}。返回1(是)或0(否)。',
    modelScore: 10,
    dataSource: '影像学检查',
    dataSourceSql: 'SELECT * FROM cardiac_mri WHERE native_t1 < 1200',
    department: '心内科',
    systemSource: '心脏核磁共振',
    部位: '心脏',
    createdAt: '2025-06-15T09:45:00Z',
    isActive: true,
    weight: 10,
    category: '心血管系统',
    reference: 'Deborde E, et al. Diagn Interv Imaging. 2020 Feb;101(2):95-107.'
  },
  {
    id: 'DR005',
    diseaseName: '法布雷病',
    ruleName: '不明原因CKD患者',
    ruleDescription: '不明原因的慢性肾病，表现为蛋白尿、血尿、肾功能异常',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: '蛋白尿|血尿|肾功能不全|慢性肾病|CKD',
    nlpScore: 4,
    // 大模型相关配置
    modelRuleMatching: true,
    modelPromptTemplate: '判断患者是否有不明原因的慢性肾病表现: {{病历文本}}。返回1(是)或0(否)。',
    modelScore: 4,
    dataSource: '实验室检查',
    dataSourceSql: 'SELECT * FROM lab_tests WHERE urine_protein > 0.3 OR glomerular_filtration_rate < 60',
    department: '肾内科',
    systemSource: '病历文书、诊断',
    部位: '肾脏',
    createdAt: '2025-06-15T09:50:00Z',
    isActive: true,
    weight: 4,
    category: '泌尿系统',
    reference: 'Lin CJ, et al. Kidney Blood Press Res. 2018;43(5):1536-1645.'
  },
  {
    id: 'DR006',
    diseaseName: '法布雷病',
    ruleName: '男性早期透析年龄<40岁',
    ruleDescription: '男性患者在40岁之前因不明原因开始透析治疗',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: '透析|血液透析|腹膜透析',
    nlpScore: 5,
    // 大模型相关配置
    modelRuleMatching: false,
    modelPromptTemplate: '',
    modelScore: 0,
    dataSource: '电子病历',
    dataSourceSql: 'SELECT * FROM patients WHERE gender = "male" AND dialysis_start_age < 40',
    department: '肾内科',
    systemSource: '病历文书',
    部位: '肾脏',
    createdAt: '2025-06-15T09:55:00Z',
    isActive: true,
    weight: 5,
    category: '泌尿系统',
    reference: 'Moleev S, et al. Nephron 2019, 141, 249–255.'
  },
  {
    id: 'DR007',
    diseaseName: '法布雷病',
    ruleName: '心肌肥厚（肥厚型心肌病）',
    ruleDescription: '超声心动图显示左心室肥厚，符合肥厚型心肌病表现',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: '心肌肥厚|肥厚型心肌病|HCM',
    nlpScore: 7,
    // 大模型相关配置
    modelRuleMatching: true,
    modelPromptTemplate: '分析超声心动图报告，判断是否存在心肌肥厚表现: {{报告内容}}。返回1(是)或0(否)。',
    modelScore: 7,
    dataSource: '影像学检查',
    dataSourceSql: 'SELECT * FROM echocardiography WHERE left_ventricular_wall_thickness > 15',
    department: '心内科',
    systemSource: '心超',
    部位: '心脏',
    createdAt: '2025-06-15T10:00:00Z',
    isActive: true,
    weight: 7,
    category: '心血管系统',
    reference: 'Rizzo R, et al. Front Cardiovasc Med. 2022 Apr 25;9:838202.'
  }
];

// 用于管理规则的钩子函数
export const useDiseaseRules = () => {
  // 在实际应用中，这里会使用API调用来获取和更新规则
  // 这里使用localStorage模拟
  const [rules, setRules] = React.useState<DiseaseRule[]>(() => {
    const savedRules = localStorage.getItem('diseaseRules');
    return savedRules ? JSON.parse(savedRules) : diseaseRules;
  });

  React.useEffect(() => {
    localStorage.setItem('diseaseRules', JSON.stringify(rules));
  }, [rules]);

  const addRule = (newRule: Omit<DiseaseRule, 'id' | 'createdAt'>) => {
    const rule: DiseaseRule = {
      ...newRule,
      id: `DR${Math.floor(1000 + Math.random() * 9000)}`,
      createdAt: new Date().toISOString()
    };
    
    setRules([...rules, rule]);
    return rule;
  };

  const updateRule = (updatedRule: DiseaseRule) => {
    setRules(rules.map(rule => 
      rule.id === updatedRule.id ? updatedRule : rule
    ));
  };

  const deleteRule = (id: string) => {
    setRules(rules.filter(rule => rule.id !== id));
  };

  return { rules, addRule, updateRule, deleteRule };
};