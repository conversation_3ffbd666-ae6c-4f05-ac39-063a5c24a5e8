# React to Vue2 转换指南

本文档详细说明了将React + TypeScript项目转换为Vue2 + Element UI项目的过程和要点。

## 项目结构对比

### React版本 (原项目)
```
src/
├── components/          # React组件
├── pages/              # 页面组件
├── types/              # TypeScript类型定义
├── mocks/              # 模拟数据
├── contexts/           # React Context
├── hooks/              # 自定义Hooks
├── lib/                # 工具库
├── App.tsx             # 根组件
└── main.tsx            # 入口文件
```

### Vue2版本 (新项目)
```
vue-version/src/
├── components/         # Vue组件
├── views/              # 页面组件
├── types/              # 类型定义和工具函数
├── mock/               # 模拟数据
├── router/             # Vue Router配置
├── store/              # Vuex状态管理
├── App.vue             # 根组件
└── main.js             # 入口文件
```

## 技术栈对比

| 功能 | React版本 | Vue2版本 |
|------|-----------|----------|
| 框架 | React 18 | Vue 2.6.14 |
| 类型系统 | TypeScript | JavaScript (可选TS) |
| 路由 | React Router v7 | Vue Router 3.x |
| 状态管理 | React Context + useState | Vuex 3.x |
| UI组件库 | Tailwind CSS + 自定义 | Element UI |
| 图表库 | Recharts | ECharts + vue-echarts |
| 构建工具 | Vite | Vue CLI |
| 样式方案 | Tailwind CSS | Element UI + 自定义CSS |

## 核心概念转换

### 1. 组件定义

**React (TSX)**
```tsx
interface Props {
  score: number;
  size?: number;
}

const RiskScoreVisualization: React.FC<Props> = ({ score, size = 120 }) => {
  return (
    <div className="risk-score-visualization">
      {/* JSX内容 */}
    </div>
  );
};
```

**Vue2 (SFC)**
```vue
<template>
  <div class="risk-score-visualization">
    <!-- 模板内容 -->
  </div>
</template>

<script>
export default {
  name: 'RiskScoreVisualization',
  props: {
    score: {
      type: Number,
      required: true
    },
    size: {
      type: Number,
      default: 120
    }
  }
}
</script>
```

### 2. 状态管理

**React (useState + Context)**
```tsx
const [isAuthenticated, setIsAuthenticated] = useState(true);

const AuthContext = createContext({
  isAuthenticated,
  setIsAuthenticated,
  logout: () => setIsAuthenticated(false)
});
```

**Vue2 (Vuex)**
```javascript
// store/index.js
export default new Vuex.Store({
  state: {
    isAuthenticated: true
  },
  mutations: {
    SET_AUTHENTICATED(state, status) {
      state.isAuthenticated = status
    }
  },
  actions: {
    logout({ commit }) {
      commit('SET_AUTHENTICATED', false)
    }
  }
})
```

### 3. 路由配置

**React Router**
```tsx
<Routes>
  <Route path="/" element={<Home />} />
  <Route path="/rules" element={<RulesPage />} />
  <Route path="/patients" element={<PatientsPage />} />
</Routes>
```

**Vue Router**
```javascript
const routes = [
  { path: '/', name: 'Home', component: Home },
  { path: '/rules', name: 'Rules', component: () => import('@/views/RulesPage.vue') },
  { path: '/patients', name: 'Patients', component: () => import('@/views/PatientsPage.vue') }
]
```

### 4. 事件处理

**React**
```tsx
const handleClick = () => {
  console.log('Clicked');
};

<button onClick={handleClick}>Click me</button>
```

**Vue2**
```vue
<template>
  <el-button @click="handleClick">Click me</el-button>
</template>

<script>
export default {
  methods: {
    handleClick() {
      console.log('Clicked');
    }
  }
}
</script>
```

### 5. 条件渲染

**React**
```tsx
{isVisible && <div>Content</div>}
{items.map(item => <div key={item.id}>{item.name}</div>)}
```

**Vue2**
```vue
<div v-if="isVisible">Content</div>
<div v-for="item in items" :key="item.id">{{ item.name }}</div>
```

## UI组件库转换

### 按钮组件

**React (Tailwind CSS)**
```tsx
<button className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
  管理规则 <i className="fa-solid fa-arrow-right ml-2"></i>
</button>
```

**Vue2 (Element UI)**
```vue
<el-button type="primary" size="medium">
  管理规则 <i class="fa-solid fa-arrow-right"></i>
</el-button>
```

### 表格组件

**React (自定义)**
```tsx
<table className="min-w-full divide-y divide-gray-200">
  <thead>
    <tr>
      <th>规则名称</th>
      <th>分类</th>
    </tr>
  </thead>
  <tbody>
    {rules.map(rule => (
      <tr key={rule.id}>
        <td>{rule.ruleName}</td>
        <td>{rule.category}</td>
      </tr>
    ))}
  </tbody>
</table>
```

**Vue2 (Element UI)**
```vue
<el-table :data="rules" stripe>
  <el-table-column prop="ruleName" label="规则名称" />
  <el-table-column prop="category" label="分类" />
</el-table>
```

## 数据流转换

### React (Props + Callbacks)
```tsx
// 父组件
const [visible, setVisible] = useState(false);

<Modal 
  visible={visible} 
  onClose={() => setVisible(false)}
  onSave={(data) => handleSave(data)}
/>

// 子组件
interface ModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (data: any) => void;
}
```

### Vue2 (Props + Events)
```vue
<!-- 父组件 -->
<Modal 
  :visible.sync="visible"
  @save="handleSave"
/>

<!-- 子组件 -->
<script>
export default {
  props: {
    visible: Boolean
  },
  computed: {
    dialogVisible: {
      get() { return this.visible },
      set(val) { this.$emit('update:visible', val) }
    }
  },
  methods: {
    handleSave(data) {
      this.$emit('save', data)
    }
  }
}
</script>
```

## 样式处理

### React (Tailwind CSS)
```tsx
<div className="flex flex-col min-h-screen bg-gradient-to-b from-blue-50 to-white">
  <main className="flex-grow container mx-auto px-4 py-8">
    {/* 内容 */}
  </main>
</div>
```

### Vue2 (Element UI + 自定义CSS)
```vue
<template>
  <div class="app-container">
    <main class="main-content">
      <div class="container">
        <!-- 内容 -->
      </div>
    </main>
  </div>
</template>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(to bottom, #f0f8ff, #ffffff);
}

.main-content {
  flex: 1;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
</style>
```

## 转换要点总结

### 1. 语法差异
- JSX → Vue模板语法
- className → class
- onClick → @click
- {expression} → {{ expression }}

### 2. 状态管理
- useState → data()
- useEffect → mounted/watch
- Context → Vuex

### 3. 组件通信
- Props传递基本相同
- Callbacks → Events ($emit)
- useContext → mapState/mapGetters

### 4. 生命周期
- useEffect → mounted/updated/destroyed
- 依赖数组 → watch

### 5. 样式方案
- Tailwind CSS → Element UI + 自定义CSS
- CSS-in-JS → Scoped CSS

## 开发建议

1. **保持功能一致性**：确保转换后的功能与原版本完全一致
2. **利用Element UI**：充分利用Element UI的组件，减少自定义开发
3. **响应式设计**：使用Element UI的栅格系统实现响应式布局
4. **状态管理**：合理使用Vuex管理复杂状态
5. **代码规范**：遵循Vue.js官方风格指南
6. **性能优化**：使用Vue的性能优化技巧，如v-show/v-if的合理选择

## 测试验证

转换完成后，需要验证以下功能：

1. ✅ 页面路由正常跳转
2. ✅ 数据展示正确
3. ✅ 交互功能正常
4. ✅ 响应式布局适配
5. ✅ 状态管理正常
6. ✅ 组件通信正常

## 后续优化

1. 添加TypeScript支持
2. 集成单元测试
3. 优化打包配置
4. 添加PWA支持
5. 性能监控和优化
