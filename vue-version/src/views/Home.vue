<template>
  <div class="home-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">疾病风险预测系统</h1>
      <p class="page-subtitle">法布雷病专项版</p>
      <div class="update-info">
        <i class="fa-solid fa-calendar-check"></i>
        数据更新时间: {{ currentDate }}
      </div>
    </div>
    
    <!-- 统计概览卡片 -->
    <el-row :gutter="20" class="stats-row">
      <!-- 平均风险得分 -->
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card class="stats-card" shadow="hover">
          <div class="stats-content">
            <div class="stats-info">
              <p class="stats-label">平均风险得分</p>
              <h3 class="stats-value">{{ averageRiskScore }}</h3>
              <p class="stats-desc">基于{{ patients.length }}例患者数据</p>
            </div>
            <div class="stats-visual">
              <RiskScoreVisualization :score="averageRiskScore" :size="80" />
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 高风险患者 -->
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card class="stats-card high-risk" shadow="hover">
          <div class="stats-content">
            <div class="stats-info">
              <p class="stats-label">高风险患者</p>
              <h3 class="stats-value danger">{{ riskDistribution.high }}</h3>
              <p class="stats-desc">需重点关注</p>
            </div>
            <div class="stats-icon danger">
              <i class="fa-solid fa-exclamation-triangle"></i>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 总评估患者 -->
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card class="stats-card" shadow="hover">
          <div class="stats-content">
            <div class="stats-info">
              <p class="stats-label">总评估患者</p>
              <h3 class="stats-value">{{ patients.length }}</h3>
              <p class="stats-desc">
                中风险: {{ riskDistribution.medium }} | 低风险: {{ riskDistribution.low }}
              </p>
            </div>
            <div class="stats-icon primary">
              <i class="fa-solid fa-users"></i>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 活跃规则数 -->
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card class="stats-card" shadow="hover">
          <div class="stats-content">
            <div class="stats-info">
              <p class="stats-label">活跃预测规则</p>
              <h3 class="stats-value success">{{ activeRulesCount }}</h3>
              <p class="stats-desc">总计: {{ diseaseRules.length }}条规则</p>
            </div>
            <div class="stats-icon success">
              <i class="fa-solid fa-list-checks"></i>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快速访问卡片 -->
    <el-row :gutter="20" class="quick-access-row">
      <el-col :xs="24" :md="12">
        <el-card class="quick-access-card rules-card" shadow="hover" @click.native="goToRules">
          <div class="quick-access-content">
            <div class="quick-access-icon">
              <i class="fa-solid fa-list-alt"></i>
            </div>
            <div class="quick-access-info">
              <h3>疾病风险预测规则</h3>
              <p>管理和配置法布雷病风险预测规则，设置匹配条件和权重。</p>
              <el-button type="primary" size="medium" class="access-btn">
                管理规则 <i class="fa-solid fa-arrow-right"></i>
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :md="12">
        <el-card class="quick-access-card patients-card" shadow="hover" @click.native="goToPatients">
          <div class="quick-access-content">
            <div class="quick-access-icon">
              <i class="fa-solid fa-user-md"></i>
            </div>
            <div class="quick-access-info">
              <h3>患者风险标记</h3>
              <p>查看患者风险评估结果，分析各项得分明细和匹配规则详情。</p>
              <el-button type="primary" size="medium" class="access-btn">
                查看患者 <i class="fa-solid fa-arrow-right"></i>
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 系统说明 -->
    <el-card class="system-info-card" shadow="hover">
      <h3 class="info-title">系统说明</h3>
      <p class="info-content">
        本系统用于法布雷病风险预测和评估，通过多维度规则匹配算法对患者风险进行评分。
        系统提供风险规则管理和患者风险标记功能，帮助医护人员快速识别高风险患者。
      </p>
    </el-card>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import RiskScoreVisualization from '@/components/RiskScoreVisualization.vue'

export default {
  name: 'Home',
  components: {
    RiskScoreVisualization
  },
  computed: {
    ...mapState(['diseaseRules', 'patients']),
    ...mapGetters(['activeRules', 'averageRiskScore', 'riskDistribution']),
    
    activeRulesCount() {
      return this.activeRules.length
    },
    
    currentDate() {
      return new Date().toLocaleDateString('zh-CN')
    }
  },
  methods: {
    goToRules() {
      this.$router.push('/rules')
    },
    
    goToPatients() {
      this.$router.push('/patients')
    }
  }
}
</script>

<style scoped>
.home-page {
  padding: 20px 0;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 20px;
  color: #606266;
  font-weight: 500;
  margin-bottom: 16px;
}

.update-info {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 20px;
  font-size: 14px;
  color: #409EFF;
  font-weight: 500;
}

.update-info i {
  margin-right: 6px;
}

.stats-row {
  margin-bottom: 30px;
}

.stats-card {
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-4px);
}

.stats-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-info {
  flex: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stats-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stats-value.danger {
  color: #F56C6C;
}

.stats-value.success {
  color: #67C23A;
}

.stats-desc {
  font-size: 12px;
  color: #C0C4CC;
}

.stats-visual {
  margin-left: 16px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-left: 16px;
}

.stats-icon.danger {
  background: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

.stats-icon.primary {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.stats-icon.success {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.quick-access-row {
  margin-bottom: 30px;
}

.quick-access-card {
  cursor: pointer;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.quick-access-card:hover {
  transform: translateY(-4px);
}

.rules-card {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05), rgba(103, 194, 58, 0.05));
}

.patients-card {
  background: linear-gradient(135deg, rgba(103, 58, 183, 0.05), rgba(156, 39, 176, 0.05));
}

.quick-access-content {
  display: flex;
  align-items: flex-start;
}

.quick-access-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-right: 20px;
  flex-shrink: 0;
}

.rules-card .quick-access-icon {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.patients-card .quick-access-icon {
  background: rgba(103, 58, 183, 0.1);
  color: #673AB7;
}

.quick-access-info {
  flex: 1;
}

.quick-access-info h3 {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 12px;
}

.quick-access-info p {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 16px;
}

.access-btn {
  border-radius: 8px;
}

.system-info-card {
  margin-bottom: 20px;
}

.info-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 12px;
}

.info-content {
  color: #606266;
  line-height: 1.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 24px;
  }
  
  .page-subtitle {
    font-size: 16px;
  }
  
  .stats-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-visual,
  .stats-icon {
    margin: 16px 0 0 0;
  }
  
  .quick-access-content {
    flex-direction: column;
    text-align: center;
  }
  
  .quick-access-icon {
    margin: 0 auto 16px auto;
  }
}
</style>
