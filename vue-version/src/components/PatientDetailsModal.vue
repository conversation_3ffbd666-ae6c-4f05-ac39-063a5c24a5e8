<template>
  <el-dialog
    title="患者详情"
    :visible.sync="dialogVisible"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="patient" class="patient-details">
      <!-- 患者基本信息 -->
      <el-card class="patient-info-card" shadow="never">
        <div slot="header" class="card-header">
          <span class="card-title">
            <i class="fa-solid fa-user"></i>
            患者基本信息
          </span>
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>患者姓名：</label>
              <span>{{ patient.patientName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>患者ID：</label>
              <span>{{ patient.patientId }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>性别：</label>
              <el-tag size="small" :type="getGenderType(patient.gender)">
                {{ getGenderText(patient.gender) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 16px;">
          <el-col :span="8">
            <div class="info-item">
              <label>风险疾病：</label>
              <span>{{ patient.riskDisease }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>最后预测时间：</label>
              <span>{{ formatDate(patient.lastPredictionTime) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>风险等级：</label>
              <el-tag
                size="medium"
                :type="getRiskLevelType(patient.riskScore)"
              >
                {{ getRiskLevel(patient.riskScore) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 风险评分概览 -->
      <el-card class="risk-overview-card" shadow="never">
        <div slot="header" class="card-header">
          <span class="card-title">
            <i class="fa-solid fa-chart-pie"></i>
            风险评分概览
          </span>
        </div>
        <div class="risk-overview">
          <div class="risk-score-display">
            <RiskScoreVisualization :score="patient.riskScore" :size="120" />
            <div class="score-info">
              <h2 class="score-value">{{ patient.riskScore }}</h2>
              <p class="score-label">总风险得分</p>
            </div>
          </div>
          <div class="risk-stats">
            <div class="stat-item">
              <span class="stat-label">匹配规则数：</span>
              <span class="stat-value">{{ matchedRulesCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总规则数：</span>
              <span class="stat-value">{{ totalRulesCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">匹配率：</span>
              <span class="stat-value">{{ matchRate }}%</span>
            </div>
          </div>
        </div>
      </el-card>
      
      <!-- 评分明细 -->
      <el-card class="scoring-details-card" shadow="never">
        <div slot="header" class="card-header">
          <span class="card-title">
            <i class="fa-solid fa-list-ul"></i>
            评分明细
          </span>
          <div class="header-actions">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-download"
              @click="exportDetails"
            >
              导出详情
            </el-button>
          </div>
        </div>
        
        <el-table
          :data="patient.scoringItems"
          stripe
          style="width: 100%"
          max-height="400"
        >
          <el-table-column prop="ruleName" label="规则名称" min-width="200" />
          <el-table-column prop="score" label="得分" width="80" align="center">
            <template slot-scope="scope">
              <span :class="getScoreClass(scope.row.isMatched)">
                {{ scope.row.score.toFixed(1) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="maxScore" label="满分" width="80" align="center" />
          <el-table-column prop="isMatched" label="匹配状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag
                size="small"
                :type="scope.row.isMatched ? 'success' : 'info'"
              >
                {{ scope.row.isMatched ? '匹配' : '未匹配' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="sourceData" label="数据来源" width="120" />
          <el-table-column prop="matchedDetails" label="匹配详情" min-width="200">
            <template slot-scope="scope">
              <el-tooltip
                :content="scope.row.matchedDetails"
                placement="top"
                :disabled="scope.row.matchedDetails.length < 30"
              >
                <span class="matched-details">
                  {{ scope.row.matchedDetails }}
                </span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="viewRuleDetail(scope.row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="generateReport">
        生成报告
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getRiskLevel, getRiskLevelType } from '@/types'
import RiskScoreVisualization from './RiskScoreVisualization.vue'

export default {
  name: 'PatientDetailsModal',
  components: {
    RiskScoreVisualization
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    patient: {
      type: Object,
      default: null
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    
    matchedRulesCount() {
      if (!this.patient) return 0
      return this.patient.scoringItems.filter(item => item.isMatched).length
    },
    
    totalRulesCount() {
      if (!this.patient) return 0
      return this.patient.scoringItems.length
    },
    
    matchRate() {
      if (!this.patient || this.totalRulesCount === 0) return 0
      return Math.round((this.matchedRulesCount / this.totalRulesCount) * 100)
    }
  },
  methods: {
    getRiskLevel,
    getRiskLevelType,
    
    getGenderText(gender) {
      const genderMap = {
        male: '男',
        female: '女',
        other: '其他'
      }
      return genderMap[gender] || '未知'
    },
    
    getGenderType(gender) {
      const typeMap = {
        male: 'primary',
        female: 'success',
        other: 'info'
      }
      return typeMap[gender] || ''
    },
    
    getScoreClass(isMatched) {
      return isMatched ? 'score-matched' : 'score-unmatched'
    },
    
    formatDate(dateString) {
      return new Date(dateString).toLocaleString('zh-CN')
    },
    
    handleClose() {
      this.dialogVisible = false
    },
    
    exportDetails() {
      this.$message.info('导出功能开发中...')
    },
    
    generateReport() {
      this.$message.info('生成报告功能开发中...')
    },
    
    viewRuleDetail(scoringItem) {
      this.$message.info(`查看规则详情: ${scoringItem.ruleName}`)
    }
  }
}
</script>

<style scoped>
.patient-details {
  max-height: 70vh;
  overflow-y: auto;
}

.patient-info-card,
.risk-overview-card,
.scoring-details-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: bold;
  color: #303133;
}

.card-title i {
  margin-right: 8px;
  color: #409EFF;
}

.info-item {
  margin-bottom: 12px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
}

.risk-overview {
  display: flex;
  align-items: center;
  gap: 40px;
}

.risk-score-display {
  display: flex;
  align-items: center;
  gap: 20px;
}

.score-info {
  text-align: center;
}

.score-value {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin: 0 0 8px 0;
}

.score-label {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.risk-stats {
  flex: 1;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 8px 16px;
  background: #f5f7fa;
  border-radius: 6px;
}

.stat-label {
  color: #606266;
  font-weight: 500;
}

.stat-value {
  color: #303133;
  font-weight: bold;
}

.score-matched {
  color: #67C23A;
  font-weight: bold;
}

.score-unmatched {
  color: #C0C4CC;
}

.matched-details {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .risk-overview {
    flex-direction: column;
    gap: 20px;
  }
  
  .risk-score-display {
    flex-direction: column;
    text-align: center;
  }
}
</style>
