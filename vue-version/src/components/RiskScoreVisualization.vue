<template>
  <div class="risk-score-visualization">
    <v-chart
      :option="chartOption"
      :style="{ width: size + 'px', height: size + 'px' }"
      autoresize
    />
    <div class="score-text" :style="{ fontSize: textSize + 'px' }">
      {{ score }}
    </div>
  </div>
</template>

<script>
import { getRiskLevelColor } from '@/types'

export default {
  name: 'RiskScoreVisualization',
  props: {
    score: {
      type: Number,
      required: true,
      default: 0
    },
    size: {
      type: Number,
      default: 120
    }
  },
  computed: {
    textSize() {
      return Math.max(12, this.size * 0.15)
    },
    
    riskColor() {
      if (this.score >= 70) return '#F56C6C'
      if (this.score >= 30) return '#E6A23C'
      return '#67C23A'
    },
    
    chartOption() {
      const percentage = Math.min(100, Math.max(0, this.score))
      
      return {
        series: [
          {
            type: 'pie',
            radius: ['70%', '90%'],
            center: ['50%', '50%'],
            startAngle: 90,
            data: [
              {
                value: percentage,
                itemStyle: {
                  color: this.riskColor,
                  borderRadius: 10
                }
              },
              {
                value: 100 - percentage,
                itemStyle: {
                  color: '#E4E7ED',
                  borderRadius: 10
                }
              }
            ],
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            animation: true,
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: 0
          }
        ],
        graphic: {
          elements: [
            {
              type: 'text',
              left: 'center',
              top: 'center',
              style: {
                text: this.score.toString(),
                fontSize: this.textSize,
                fontWeight: 'bold',
                fill: this.riskColor
              }
            }
          ]
        }
      }
    }
  }
}
</script>

<style scoped>
.risk-score-visualization {
  position: relative;
  display: inline-block;
}

.score-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: bold;
  pointer-events: none;
  z-index: 10;
}
</style>
