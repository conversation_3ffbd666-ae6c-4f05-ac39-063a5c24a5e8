# 疾病风险预测系统 - Vue2版本

法布雷病专项版风险预测系统的Vue2 + Element UI实现版本。

## 项目概述

本项目是基于原React版本转换而来的Vue2应用，保持了相同的功能和用户体验，采用Vue2 + Element UI技术栈重新实现。

### 主要功能

1. **首页概览** - 显示系统统计数据和快速访问入口
2. **规则管理** - 管理和配置法布雷病风险预测规则
3. **患者管理** - 查看患者风险评估结果和详细分析

## 技术栈

### 前端框架
- **Vue 2.6.14** - 渐进式JavaScript框架
- **Vue Router 3.x** - 官方路由管理器
- **Vuex 3.x** - 状态管理模式

### UI组件库
- **Element UI 2.15.14** - 基于Vue的桌面端组件库
- **Font Awesome 6.x** - 图标库

### 图表可视化
- **ECharts 5.x** - 数据可视化图表库
- **vue-echarts** - Vue的ECharts组件

### 开发工具
- **Vue CLI 5.x** - Vue项目脚手架
- **ESLint** - 代码质量检查工具

## 项目结构

```
vue-version/
├── public/                 # 静态资源
│   └── index.html         # HTML模板
├── src/
│   ├── components/        # 可复用组件
│   │   ├── Navbar.vue                    # 导航栏
│   │   ├── RiskScoreVisualization.vue    # 风险评分可视化
│   │   ├── RuleEditorModal.vue           # 规则编辑器
│   │   └── PatientDetailsModal.vue       # 患者详情
│   ├── views/             # 页面组件
│   │   ├── Home.vue                      # 首页
│   │   ├── RulesPage.vue                 # 规则管理页
│   │   └── PatientsPage.vue              # 患者管理页
│   ├── router/            # 路由配置
│   │   └── index.js
│   ├── store/             # Vuex状态管理
│   │   └── index.js
│   ├── mock/              # 模拟数据
│   │   ├── diseaseRules.js
│   │   └── patients.js
│   ├── types/             # 类型定义和工具函数
│   │   └── index.js
│   ├── App.vue            # 根组件
│   └── main.js            # 应用入口
├── package.json           # 项目配置
├── vue.config.js          # Vue CLI配置
└── README.md              # 项目说明
```

## 开发环境搭建

### 环境要求

- Node.js >= 14.x
- npm >= 6.x 或 yarn >= 1.x

### 安装依赖

```bash
cd vue-version
npm install
# 或
yarn install
```

### 启动开发服务器

```bash
npm run serve
# 或
yarn serve
```

访问 http://localhost:3001 查看应用

### 构建生产版本

```bash
npm run build
# 或
yarn build
```

### 代码检查

```bash
npm run lint
# 或
yarn lint
```

## 核心功能说明

### 1. 首页概览 (Home.vue)

- 显示系统统计数据（平均风险得分、高风险患者数等）
- 提供快速访问入口到规则管理和患者管理
- 使用ECharts进行数据可视化

### 2. 规则管理 (RulesPage.vue)

- 规则列表展示和筛选
- 支持按分类、状态筛选规则
- 规则的增删改查操作
- 规则详情查看和编辑

### 3. 患者管理 (PatientsPage.vue)

- 患者列表展示和筛选
- 风险等级可视化显示
- 患者详情查看
- 评分明细分析

### 4. 状态管理 (Vuex)

- 集中管理应用状态
- 包含规则数据、患者数据、认证状态等
- 提供统一的数据操作接口

## 组件说明

### RiskScoreVisualization.vue
风险评分可视化组件，使用ECharts绘制环形进度图显示风险得分。

### RuleEditorModal.vue
规则编辑器模态框，支持规则的查看、编辑和新增，包含基本信息、NLP配置、大模型配置和数据源配置等标签页。

### PatientDetailsModal.vue
患者详情模态框，显示患者基本信息、风险评分概览和评分明细。

### Navbar.vue
应用导航栏，包含品牌标识、导航菜单和用户操作区域。

## 数据模型

### 疾病规则 (DiseaseRule)
```javascript
{
  id: String,              // 规则ID
  diseaseName: String,     // 疾病名称
  ruleName: String,        // 规则名称
  ruleDescription: String, // 规则描述
  nlpMatching: Boolean,    // 是否启用NLP匹配
  nlpRegexPattern: String, // NLP正则表达式
  nlpScore: Number,        // NLP匹配得分
  // ... 其他字段
}
```

### 患者 (Patient)
```javascript
{
  id: String,              // 患者ID
  patientName: String,     // 患者姓名
  patientId: String,       // 患者编号
  gender: String,          // 性别
  riskScore: Number,       // 风险得分
  scoringItems: Array,     // 评分项目
  // ... 其他字段
}
```

## 样式设计

- 采用Element UI的设计语言
- 响应式布局，支持移动端和桌面端
- 使用CSS3动画效果提升用户体验
- 统一的颜色主题和间距规范

## 与React版本的差异

1. **语法差异**：Vue的模板语法 vs React的JSX
2. **状态管理**：Vuex vs React的useState/useContext
3. **生命周期**：Vue的生命周期钩子 vs React的useEffect
4. **组件通信**：Vue的props/emit vs React的props/callback
5. **UI组件库**：Element UI vs Tailwind CSS + 自定义组件

## 开发注意事项

1. 遵循Vue 2.x的最佳实践
2. 合理使用Element UI组件，避免过度自定义
3. 保持组件的单一职责原则
4. 注意响应式设计，确保在不同设备上的良好体验
5. 使用ESLint保持代码质量

## 部署说明

1. 执行 `npm run build` 构建生产版本
2. 将 `dist` 目录部署到Web服务器
3. 配置服务器支持Vue Router的history模式
4. 确保静态资源正确加载

## 后续开发计划

1. 添加用户认证和权限管理
2. 集成真实的后端API
3. 添加数据导出功能
4. 优化移动端体验
5. 添加单元测试和E2E测试

## 技术支持

如有问题或建议，请联系开发团队。
