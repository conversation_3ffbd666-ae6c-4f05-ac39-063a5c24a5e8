// 疾病规则数据结构
export const DiseaseRuleSchema = {
  id: '',
  diseaseName: '',
  ruleName: '',
  ruleDescription: '',
  // NLP相关配置
  nlpMatching: false,
  nlpRegexPattern: '',
  nlpScore: 0,
  // 大模型相关配置
  modelRuleMatching: false,
  modelPromptTemplate: '',
  modelScore: 0,
  dataSource: '',
  dataSourceSql: '',
  department: '',
  systemSource: '',
  部位: '',
  createdAt: '',
  isActive: true,
  weight: 0,
  category: '',
  reference: ''
}

// 评分项数据结构
export const ScoringItemSchema = {
  id: '',
  ruleId: '',
  ruleName: '',
  score: 0,
  maxScore: 0,
  isMatched: false,
  matchedDetails: '',
  sourceData: '',
  referenceValue: '',
  description: ''
}

// 患者数据结构
export const PatientSchema = {
  id: '',
  patientName: '',
  patientId: '',
  gender: 'male', // 'male' | 'female' | 'other'
  riskDisease: '',
  riskScore: 0,
  scoringItems: [],
  lastPredictionTime: ''
}

// 风险等级枚举
export const RiskLevel = {
  LOW: '低风险',
  MEDIUM: '中风险',
  HIGH: '高风险'
}

// 风险预测数据结构
export const RiskPredictionSchema = {
  diseaseName: '',
  riskScore: 0,
  riskLevel: RiskLevel.LOW,
  predictionTime: ''
}

// 工具函数：获取风险等级
export function getRiskLevel(score) {
  if (score >= 70) return RiskLevel.HIGH
  if (score >= 30) return RiskLevel.MEDIUM
  return RiskLevel.LOW
}

// 工具函数：获取风险等级颜色
export function getRiskLevelColor(score) {
  if (score >= 70) return 'danger'
  if (score >= 30) return 'warning'
  return 'success'
}

// 工具函数：获取风险等级标签类型
export function getRiskLevelType(score) {
  if (score >= 70) return 'danger'
  if (score >= 30) return 'warning'
  return 'success'
}
