import Vue from 'vue'
import VueRouter from 'vue-router'
import Home from '@/views/Home.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页 - 疾病风险预测系统'
    }
  },
  {
    path: '/rules',
    name: 'Rules',
    component: () => import('@/views/RulesPage.vue'),
    meta: {
      title: '规则管理 - 疾病风险预测系统'
    }
  },
  {
    path: '/patients',
    name: 'Patients',
    component: () => import('@/views/PatientsPage.vue'),
    meta: {
      title: '患者管理 - 疾病风险预测系统'
    }
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title
  }
  next()
})

export default router
