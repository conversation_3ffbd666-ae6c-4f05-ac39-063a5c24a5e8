import React from 'react';
import { Patient, RiskLevel } from '@/types';
import RiskScoreVisualization from './RiskScoreVisualization';

interface PatientDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  patient: Patient | null;
}

// 根据得分确定风险等级
const getRiskLevel = (score: number): RiskLevel => {
  if (score >= 70) return RiskLevel.HIGH;
  if (score >= 30) return RiskLevel.MEDIUM;
  return RiskLevel.LOW;
};

const PatientDetailsModal: React.FC<PatientDetailsModalProps> = ({
  isOpen,
  onClose,
  patient
}) => {
  if (!isOpen || !patient) return null;
  
  const riskLevel = getRiskLevel(patient.riskScore);
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-900">
              患者风险详情
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <i className="fa-solid fa-times text-xl"></i>
            </button>
          </div>
        </div>
        
        <div className="p-6">
           {/* 患者基本信息和风险评估概览 */}
           <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
             {/* 风险评分卡片 */}
             <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl shadow-sm border border-blue-100 p-5 flex flex-col items-center justify-center transform transition-all hover:shadow-md">
               <RiskScoreVisualization score={patient.riskScore} size={140} />
               <div className="mt-4 text-center">
                 <h3 className="text-lg font-bold text-gray-900">{patient.patientName}</h3>
                 <p className="text-gray-500 text-sm">{patient.patientId}</p>
                 <div className="mt-2 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                   {riskLevel === RiskLevel.HIGH 
                     ? 'bg-red-100 text-red-800 shadow-sm' 
                     : riskLevel === RiskLevel.MEDIUM
                       ? 'bg-yellow-100 text-yellow-800 shadow-sm'
                       : 'bg-green-100 text-green-800 shadow-sm'}">
                   {riskLevel}
                 </div>
                 
                 {/* 风险提示 */}
                 {patient.riskScore >= 10 && (
                   <div className="mt-3 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                     <i className="fa-solid fa-exclamation-circle mr-1"></i>
                     总分超过10分，建议进一步检查
                   </div>
                 )}
               </div>
             </div>
             
             {/* 患者基本信息 */}
             <div className="md:col-span-2 bg-white rounded-xl shadow-sm border border-gray-100 p-5">
               <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                 <i className="fa-solid fa-user-md text-blue-600 mr-2"></i>患者基本信息
               </h3>
               <div className="grid grid-cols-1 sm:grid-cols-3 gap-x-6 gap-y-4">
                 <div>
                   <p className="text-sm text-gray-500">性别</p>
                   <p className="font-medium">{patient.gender === 'male' ? '男' : '女'}</p>
                 </div>
                 <div>
                   <p className="text-sm text-gray-500">预测病种</p>
                   <p className="font-medium">{patient.riskDisease}</p>
                 </div>
                 <div>
                   <p className="text-sm text-gray-500">风险得分</p>
                   <p className="font-medium text-xl">{patient.riskScore} / 100</p>
                 </div>
                 <div>
                   <p className="text-sm text-gray-500">最后评估时间</p>
                   <p className="font-medium">{patient.lastPredictionTime}</p>
                 </div>
                 <div>
                   <p className="text-sm text-gray-500">匹配规则数量</p>
                   <p className="font-medium">{patient.scoringItems.filter(item => item.isMatched).length} / {patient.scoringItems.length}</p>
                 </div>
                 <div>
                   <p className="text-sm text-gray-500">主要风险系统</p>
                   <p className="font-medium">
                     {(() => {
                       const systemCounts = {
                         '心血管系统': 0,
                         '泌尿系统': 0,
                         '神经系统': 0,
                         '皮肤系统': 0
                       };
                       
                       patient.scoringItems.forEach(item => {
                         if (item.isMatched) {
                           if (item.ruleName.includes('心脏') || item.ruleName.includes('心肌')) {
                             systemCounts['心血管系统']++;
                           } else if (item.ruleName.includes('肾') || item.ruleName.includes('透析')) {
                             systemCounts['泌尿系统']++;
                           } else if (item.ruleName.includes('神经') || item.ruleName.includes('肢端')) {
                             systemCounts['神经系统']++;
                           } else if (item.ruleName.includes('皮肤')) {
                             systemCounts['皮肤系统']++;
                           }
                         }
                       });
                       
                       const maxSystem = Object.entries(systemCounts).sort((a, b) => b[1] - a[1])[0];
                       return maxSystem[1] > 0 ? maxSystem[0] : '未明确';
                     })()}
                   </p>
                 </div>
               </div>
             </div>
           </div>
           
           {/* 风险评估建议 */}
           {patient.riskScore >= 10 && (
             <div className="bg-orange-50 border border-orange-100 rounded-lg p-4 mb-8">
               <div className="flex">
                 <div className="flex-shrink-0">
                   <i className="fa-solid fa-exclamation-triangle text-orange-500 mt-0.5"></i>
                 </div>
                 <div className="ml-3">
                   <h3 className="text-sm font-medium text-orange-800">法布雷病高危风险提示</h3>
                   <div className="mt-2 text-sm text-orange-700">
                     <p>患者风险评分超过阈值，建议进行以下检查以明确诊断：</p>
                     <ul className="mt-1 list-disc pl-5 space-y-1">
                       <li>α-半乳糖苷酶A活性检测</li>
                       <li>GLA基因突变检测</li>
                       <li>全面的心脏评估（包括超声心动图和心脏磁共振）</li>
                       <li>肾功能评估和尿沉渣检查</li>
                     </ul>
                   </div>
                 </div>
               </div>
             </div>
           )}
          
          {/* 详细得分项 */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <i className="fa-solid fa-list-checks text-indigo-600 mr-2"></i>风险评估明细
            </h3>
            
            <div className="overflow-x-auto rounded-lg border border-gray-200">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                     <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则名称</th>
                     <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">得分</th>
                     <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">匹配状态</th>
                     <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">匹配详情</th>
                     <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">来源数据</th>
                   </tr>
                 </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {patient.scoringItems.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                       <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                         {item.ruleName}
                       </td>
                       <td className="px-4 py-3 whitespace-nowrap">
                         <div className="flex items-center">
                           <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden mr-2">
                             <div 
                               className={`h-full ${item.isMatched ? 'bg-green-500' : 'bg-gray-300'}`}
                               style={{ width: `${(item.score / item.maxScore) * 100}%` }}
                             ></div>
                           </div>
                           <span className="text-sm">{item.score} / {item.maxScore}</span>
                         </div>
                       </td>
                       <td className="px-4 py-3 whitespace-nowrap">
                         <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                           ${item.isMatched ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                           {item.isMatched ? '已匹配' : '未匹配'}
                         </span>
                       </td>
                       <td className="px-4 py-3 text-sm text-gray-700">
                         {item.matchedDetails}
                         {item.referenceValue && (
                           <p className="text-xs text-gray-500 mt-1">参考值范围: {item.referenceValue}</p>
                         )}
                       </td>
                       <td className="px-4 py-3 text-sm text-gray-600 font-mono bg-gray-50 rounded">
                         {item.sourceData}
                       </td>
                     </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-50 px-6 py-4 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            关闭详情
          </button>
        </div>
      </div>
    </div>
  );
};

export default PatientDetailsModal;