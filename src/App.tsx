import { Routes, Route } from "react-router-dom";
import Home from "@/pages/Home";
import RulesPage from "@/pages/RulesPage";
import PatientsPage from "@/pages/PatientsPage";
import Navbar from "@/components/Navbar";
import { useState } from "react";
import { AuthContext } from '@/contexts/authContext';

export default function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(true);

  const logout = () => {
    setIsAuthenticated(false);
  };

  return (
    <AuthContext.Provider
      value={{ isAuthenticated, setIsAuthenticated, logout }}
    >
      <div className="flex flex-col min-h-screen bg-gradient-to-b from-blue-50 to-white">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/rules" element={<RulesPage />} />
            <Route path="/patients" element={<PatientsPage />} />
          </Routes>
        </main>
        <footer className="bg-white/80 backdrop-blur-sm border-t border-gray-200 py-4 text-center text-sm text-gray-500 shadow-sm">
          © 2025 疾病风险预测系统 - 法布雷病专项版 | 数据仅供参考，不构成医疗建议
        </footer>
      </div>
    </AuthContext.Provider>
  );
}
