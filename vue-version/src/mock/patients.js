import { diseaseRules } from './diseaseRules'

// 生成随机风险得分
const getRandomRiskScore = () => {
  return Math.floor(Math.random() * 100) + 1
}

// 生成患者的评分项目
const generateScoringItems = (patientId) => {
  // 获取活跃的规则
  const activeRules = diseaseRules.filter(rule => rule.isActive)
  
  // 为每个规则生成评分项
  return activeRules.map((rule, index) => {
    // 随机决定是否匹配该规则
    const isMatched = Math.random() > 0.3
    
    // 根据规则权重和匹配状态生成得分
    const maxScore = rule.weight
    const score = isMatched ? maxScore * (0.8 + Math.random() * 0.2) : Math.random() * maxScore * 0.3
    
    // 生成描述
    const descriptions = [
      '患者表现出与该规则高度吻合的特征',
      '部分符合该规则描述的临床表现',
      '检查结果显示与该规则有一定关联',
      '存在该规则提及的部分指标异常',
      '临床症状与该规则描述有相似之处'
    ]
    
    // 生成匹配详情和来源数据
    const matchDetails = [
      '患者主诉存在明显的肢端疼痛症状',
      '体格检查发现皮肤血管角质瘤',
      '超声心动图显示乳头肌肥大',
      '心脏磁共振检查显示Native T1值降低',
      '实验室检查发现蛋白尿和血尿',
      '患者有血液透析史，开始透析年龄为38岁',
      '超声心动图显示左心室肥厚'
    ]
    
    const referenceValues = [
      '正常范围: 1200-1400 ms',
      '正常范围: <3.6 cm²',
      '正常范围: 0.12-0.2 s',
      '正常范围: <130/80 mmHg',
      '正常范围: 0-0.3 g/24h'
    ]
    
    const sourceDataOptions = [
      '电子病历-主诉',
      '体格检查报告',
      '超声心动图报告',
      '心脏磁共振报告',
      '实验室检查报告',
      '透析记录',
      '影像学检查'
    ]
    
    return {
      id: `${patientId}_${rule.id}`,
      ruleId: rule.id,
      ruleName: rule.ruleName,
      score: Math.round(score * 100) / 100,
      maxScore,
      isMatched,
      matchedDetails: matchDetails[Math.floor(Math.random() * matchDetails.length)],
      sourceData: sourceDataOptions[Math.floor(Math.random() * sourceDataOptions.length)],
      referenceValue: referenceValues[Math.floor(Math.random() * referenceValues.length)],
      description: descriptions[Math.floor(Math.random() * descriptions.length)]
    }
  })
}

// 生成模拟患者数据
export const patients = [
  {
    id: 'P001',
    patientName: '张**',
    patientId: 'H2024001',
    gender: 'male',
    riskDisease: '法布雷病',
    riskScore: 85,
    scoringItems: [],
    lastPredictionTime: '2025-01-10T14:30:00Z'
  },
  {
    id: 'P002',
    patientName: '李**',
    patientId: 'H2024002',
    gender: 'female',
    riskDisease: '法布雷病',
    riskScore: 72,
    scoringItems: [],
    lastPredictionTime: '2025-01-10T15:45:00Z'
  },
  {
    id: 'P003',
    patientName: '王**',
    patientId: 'H2024003',
    gender: 'male',
    riskDisease: '法布雷病',
    riskScore: 45,
    scoringItems: [],
    lastPredictionTime: '2025-01-10T16:20:00Z'
  },
  {
    id: 'P004',
    patientName: '陈**',
    patientId: 'H2024004',
    gender: 'female',
    riskDisease: '法布雷病',
    riskScore: 28,
    scoringItems: [],
    lastPredictionTime: '2025-01-10T17:10:00Z'
  },
  {
    id: 'P005',
    patientName: '刘**',
    patientId: 'H2024005',
    gender: 'male',
    riskDisease: '法布雷病',
    riskScore: 91,
    scoringItems: [],
    lastPredictionTime: '2025-01-10T18:00:00Z'
  },
  {
    id: 'P006',
    patientName: '赵**',
    patientId: 'H2024006',
    gender: 'female',
    riskDisease: '法布雷病',
    riskScore: 63,
    scoringItems: [],
    lastPredictionTime: '2025-01-11T09:15:00Z'
  },
  {
    id: 'P007',
    patientName: '孙**',
    patientId: 'H2024007',
    gender: 'male',
    riskDisease: '法布雷病',
    riskScore: 38,
    scoringItems: [],
    lastPredictionTime: '2025-01-11T10:30:00Z'
  },
  {
    id: 'P008',
    patientName: '周**',
    patientId: 'H2024008',
    gender: 'female',
    riskDisease: '法布雷病',
    riskScore: 76,
    scoringItems: [],
    lastPredictionTime: '2025-01-11T11:45:00Z'
  },
  {
    id: 'P009',
    patientName: '吴**',
    patientId: 'H2024009',
    gender: 'male',
    riskDisease: '法布雷病',
    riskScore: 52,
    scoringItems: [],
    lastPredictionTime: '2025-01-11T13:20:00Z'
  },
  {
    id: 'P010',
    patientName: '郑**',
    patientId: 'H2024010',
    gender: 'female',
    riskDisease: '法布雷病',
    riskScore: 19,
    scoringItems: [],
    lastPredictionTime: '2025-01-11T14:50:00Z'
  }
]

// 为每个患者生成评分项目
patients.forEach(patient => {
  patient.scoringItems = generateScoringItems(patient.id)
})
