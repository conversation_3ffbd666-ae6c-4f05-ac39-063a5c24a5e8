<template>
  <nav class="navbar">
    <div class="navbar-container">
      <!-- Logo和标题 -->
      <div class="navbar-brand">
        <router-link to="/" class="brand-link">
          <i class="fa-solid fa-heartbeat brand-icon"></i>
          <span class="brand-text">疾病风险预测系统</span>
        </router-link>
      </div>
      
      <!-- 导航菜单 -->
      <div class="navbar-menu">
        <el-menu
          :default-active="activeIndex"
          mode="horizontal"
          background-color="transparent"
          text-color="#333"
          active-text-color="#409EFF"
          class="navbar-nav"
          @select="handleSelect"
        >
          <el-menu-item index="/">
            <i class="fa-solid fa-home"></i>
            <span>首页</span>
          </el-menu-item>
          <el-menu-item index="/rules">
            <i class="fa-solid fa-list-alt"></i>
            <span>规则管理</span>
          </el-menu-item>
          <el-menu-item index="/patients">
            <i class="fa-solid fa-user-md"></i>
            <span>患者管理</span>
          </el-menu-item>
        </el-menu>
      </div>
      
      <!-- 用户操作区域 -->
      <div class="navbar-actions">
        <el-dropdown @command="handleCommand">
          <span class="user-dropdown">
            <i class="fa-solid fa-user-circle user-icon"></i>
            <span class="user-name">管理员</span>
            <i class="el-icon-arrow-down"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="profile">
              <i class="fa-solid fa-user"></i>
              个人资料
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <i class="fa-solid fa-cog"></i>
              系统设置
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <i class="fa-solid fa-sign-out-alt"></i>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
  </nav>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'Navbar',
  computed: {
    ...mapState(['isAuthenticated']),
    activeIndex() {
      return this.$route.path
    }
  },
  methods: {
    ...mapActions(['logout']),
    
    handleSelect(key) {
      if (key !== this.$route.path) {
        this.$router.push(key)
      }
    },
    
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$message.info('个人资料功能开发中...')
          break
        case 'settings':
          this.$message.info('系统设置功能开发中...')
          break
        case 'logout':
          this.handleLogout()
          break
      }
    },
    
    handleLogout() {
      this.$confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.logout()
        this.$message.success('已退出登录')
        // 这里可以添加跳转到登录页的逻辑
      }).catch(() => {
        // 用户取消
      })
    }
  }
}
</script>

<style scoped>
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.navbar-brand {
  flex-shrink: 0;
}

.brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #333;
  font-weight: 600;
  font-size: 18px;
  transition: color 0.3s ease;
}

.brand-link:hover {
  color: #409EFF;
}

.brand-icon {
  font-size: 24px;
  margin-right: 8px;
  color: #409EFF;
}

.brand-text {
  white-space: nowrap;
}

.navbar-menu {
  flex: 1;
  display: flex;
  justify-content: center;
}

.navbar-nav {
  border-bottom: none !important;
}

.navbar-nav .el-menu-item {
  border-bottom: none !important;
  margin: 0 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.navbar-nav .el-menu-item:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.navbar-nav .el-menu-item.is-active {
  background-color: rgba(64, 158, 255, 0.1);
  border-bottom: none !important;
}

.navbar-nav .el-menu-item i {
  margin-right: 6px;
}

.navbar-actions {
  flex-shrink: 0;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.user-dropdown:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.user-icon {
  font-size: 20px;
  margin-right: 6px;
  color: #409EFF;
}

.user-name {
  margin-right: 6px;
  font-weight: 500;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navbar-container {
    padding: 0 16px;
  }
  
  .brand-text {
    display: none;
  }
  
  .navbar-nav .el-menu-item span {
    display: none;
  }
  
  .user-name {
    display: none;
  }
}

@media (max-width: 480px) {
  .navbar-nav .el-menu-item {
    margin: 0 4px;
    padding: 0 12px;
  }
}
</style>
