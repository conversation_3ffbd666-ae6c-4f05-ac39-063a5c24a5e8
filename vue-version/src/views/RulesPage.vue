<template>
  <div class="rules-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">疾病风险预测规则管理</h1>
      <p class="page-subtitle">配置和管理法布雷病风险预测规则</p>
    </div>
    
    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索规则名称或描述"
          prefix-icon="el-icon-search"
          style="width: 300px;"
          clearable
        />
        <el-select
          v-model="filterCategory"
          placeholder="筛选分类"
          style="width: 150px; margin-left: 12px;"
          clearable
        >
          <el-option label="全部分类" value="" />
          <el-option
            v-for="category in categories"
            :key="category"
            :label="category"
            :value="category"
          />
        </el-select>
        <el-select
          v-model="filterStatus"
          placeholder="筛选状态"
          style="width: 120px; margin-left: 12px;"
          clearable
        >
          <el-option label="全部状态" value="" />
          <el-option label="启用" value="active" />
          <el-option label="禁用" value="inactive" />
        </el-select>
      </div>
      <div class="action-right">
        <el-button type="primary" icon="el-icon-plus" @click="showAddDialog">
          新增规则
        </el-button>
      </div>
    </div>
    
    <!-- 规则列表 -->
    <el-card class="rules-table-card" shadow="hover">
      <el-table
        :data="filteredRules"
        v-loading="loading"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="规则ID" width="100" />
        <el-table-column prop="ruleName" label="规则名称" min-width="200" />
        <el-table-column prop="category" label="分类" width="120">
          <template slot-scope="scope">
            <el-tag size="small" :type="getCategoryType(scope.row.category)">
              {{ scope.row.category }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="department" label="科室" width="120" />
        <el-table-column prop="weight" label="权重" width="80" sortable />
        <el-table-column prop="isActive" label="状态" width="80">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.isActive"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-view"
              @click="viewRule(scope.row)"
            >
              查看
            </el-button>
            <el-button
              size="mini"
              type="warning"
              icon="el-icon-edit"
              @click="editRule(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="deleteRule(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredRules.length"
        />
      </div>
    </el-card>
    
    <!-- 规则详情/编辑对话框 -->
    <RuleEditorModal
      :visible.sync="dialogVisible"
      :rule="currentRule"
      :mode="dialogMode"
      @save="handleSaveRule"
    />
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import RuleEditorModal from '@/components/RuleEditorModal.vue'

export default {
  name: 'RulesPage',
  components: {
    RuleEditorModal
  },
  data() {
    return {
      searchKeyword: '',
      filterCategory: '',
      filterStatus: '',
      currentPage: 1,
      pageSize: 20,
      dialogVisible: false,
      dialogMode: 'view', // 'view', 'edit', 'add'
      currentRule: null
    }
  },
  computed: {
    ...mapState(['diseaseRules', 'loading']),
    
    categories() {
      const categories = [...new Set(this.diseaseRules.map(rule => rule.category))]
      return categories.filter(Boolean)
    },
    
    filteredRules() {
      let rules = [...this.diseaseRules]
      
      // 搜索过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        rules = rules.filter(rule =>
          rule.ruleName.toLowerCase().includes(keyword) ||
          rule.ruleDescription.toLowerCase().includes(keyword)
        )
      }
      
      // 分类过滤
      if (this.filterCategory) {
        rules = rules.filter(rule => rule.category === this.filterCategory)
      }
      
      // 状态过滤
      if (this.filterStatus) {
        const isActive = this.filterStatus === 'active'
        rules = rules.filter(rule => rule.isActive === isActive)
      }
      
      return rules
    }
  },
  methods: {
    ...mapActions(['addRule', 'updateRule', 'deleteRule']),
    
    getCategoryType(category) {
      const typeMap = {
        '神经系统': 'primary',
        '皮肤系统': 'success',
        '心血管系统': 'danger',
        '泌尿系统': 'warning',
        '眼科系统': 'info',
        '耳鼻喉系统': '',
        '消化系统': 'success'
      }
      return typeMap[category] || ''
    },
    
    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('zh-CN')
    },
    
    handleSortChange({ column, prop, order }) {
      // 处理排序逻辑
      console.log('Sort change:', { column, prop, order })
    },
    
    handleSizeChange(val) {
      this.pageSize = val
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
    },
    
    async handleStatusChange(rule) {
      try {
        await this.updateRule(rule)
        this.$message.success('规则状态更新成功')
      } catch (error) {
        this.$message.error('规则状态更新失败')
        // 回滚状态
        rule.isActive = !rule.isActive
      }
    },
    
    showAddDialog() {
      this.currentRule = null
      this.dialogMode = 'add'
      this.dialogVisible = true
    },
    
    viewRule(rule) {
      this.currentRule = rule
      this.dialogMode = 'view'
      this.dialogVisible = true
    },
    
    editRule(rule) {
      this.currentRule = rule
      this.dialogMode = 'edit'
      this.dialogVisible = true
    },
    
    async deleteRule(rule) {
      try {
        await this.$confirm(`确定要删除规则"${rule.ruleName}"吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await this.deleteRule(rule.id)
        this.$message.success('规则删除成功')
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('规则删除失败')
        }
      }
    },
    
    async handleSaveRule(rule) {
      try {
        if (this.dialogMode === 'add') {
          await this.addRule(rule)
          this.$message.success('规则添加成功')
        } else {
          await this.updateRule(rule)
          this.$message.success('规则更新成功')
        }
        this.dialogVisible = false
      } catch (error) {
        this.$message.error('操作失败')
      }
    }
  }
}
</script>

<style scoped>
.rules-page {
  padding: 20px 0;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 16px;
  color: #606266;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.action-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.rules-table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-left {
    justify-content: center;
  }
  
  .action-right {
    text-align: center;
  }
  
  .pagination-wrapper {
    text-align: center;
  }
}
</style>
