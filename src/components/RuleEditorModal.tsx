import React, { useState, useEffect } from 'react';
import { DiseaseRule } from '@/types';

interface RuleEditorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (rule: DiseaseRule) => void;
  initialRule?: DiseaseRule;
}

const RuleEditorModal: React.FC<RuleEditorModalProps> = ({
  isOpen,
  onClose,
  onSave,
  initialRule
}) => {
  const [rule, setRule] = useState<DiseaseRule>({
    id: '',
    diseaseName: '法布雷病',
    ruleName: '',
    ruleDescription: '',
    // NLP相关配置
    nlpMatching: false,
    nlpRegexPattern: '',
    nlpScore: 0,
    // 大模型相关配置
    modelRuleMatching: false,
    modelPromptTemplate: '',
    modelScore: 0,
    dataSource: '',
    dataSourceSql: '',
    department: '',
    systemSource: '',
    部位: '',
    createdAt: new Date().toISOString(),
    isActive: true,
    weight: 10,
    category: '',
    reference: ''
  });

  useEffect(() => {
    if (initialRule) {
      setRule(initialRule);
    } else {
      // 生成新ID
      const newId = `DR${Math.floor(1000 + Math.random() * 9000)}`;
      setRule({
        id: newId,
        diseaseName: '法布雷病',
        ruleName: '',
        ruleDescription: '',
        nlpMatching: false,
        modelRuleMatching: false,
        dataSource: '',
        createdAt: new Date().toISOString(),
        isActive: true,
        weight: 10
      });
    }
  }, [initialRule]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target;
    setRule(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({ ...rule });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-900">
              {initialRule ? '编辑规则' : '添加新规则'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <i className="fa-solid fa-times text-xl"></i>
            </button>
          </div>
        </div>
        
         <form onSubmit={handleSubmit} className="p-6 space-y-5">
           <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
             <div>
               <label className="block text-sm font-medium text-gray-700 mb-1">
                 病种名称
               </label>
               <input
                 type="text"
                 name="diseaseName"
                 value={rule.diseaseName}
                 onChange={handleChange}
                 className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                 disabled
               />
             </div>
             
             <div>
               <label className="block text-sm font-medium text-gray-700 mb-1">
                 规则分类
               </label>
               <input
                 type="text"
                 name="category"
                 value={rule.category}
                 onChange={handleChange}
                 className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
               />
             </div>
             
             <div className="md:col-span-2">
               <label className="block text-sm font-medium text-gray-700 mb-1">
                 规则名称 <span className="text-red-500">*</span>
               </label>
               <input
                 type="text"
                 name="ruleName"
                 value={rule.ruleName}
                 onChange={handleChange}
                 className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                 required
               />
             </div>
             
             <div className="md:col-span-2">
               <label className="block text-sm font-medium text-gray-700 mb-1">
                 规则描述 <span className="text-red-500">*</span>
               </label>
               <textarea
                 name="ruleDescription"
                 value={rule.ruleDescription}
                 onChange={handleChange}
                 rows={3}
                 className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                 required
               ></textarea>
             </div>
             
             <div>
               <label className="block text-sm font-medium text-gray-700 mb-1">
                 科室来源
               </label>
               <input
                 type="text"
                 name="department"
                 value={rule.department}
                 onChange={handleChange}
                 className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
               />
             </div>
             
             <div>
               <label className="block text-sm font-medium text-gray-700 mb-1">
                 系统来源
               </label>
               <input
                 type="text"
                 name="systemSource"
                 value={rule.systemSource}
                 onChange={handleChange}
                 className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
               />
             </div>
             
             <div>
               <label className="block text-sm font-medium text-gray-700 mb-1">
                 部位
               </label>
               <input
                 type="text"
                 name="部位"
                 value={rule.部位}
                 onChange={handleChange}
                 className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
               />
             </div>
             
             <div>
               <label className="block text-sm font-medium text-gray-700 mb-1">
                 规则权重
               </label>
               <input
                 type="number"
                 name="weight"
                 value={rule.weight}
                 onChange={handleChange}
                 min="1"
                 max="20"
                 className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
               />
               <p className="text-xs text-gray-500 mt-1">权重越高，对最终得分影响越大 (1-20)</p>
             </div>
           </div>
           
           {/* NLP匹配配置 */}
           <div className={`p-4 bg-blue-50 rounded-lg transition-all duration-300 ${rule.nlpMatching ? 'border border-blue-200' : 'border border-dashed border-gray-200'}`}>
             <div className="flex items-center">
               <input
                 type="checkbox"
                 name="nlpMatching"
                 checked={rule.nlpMatching}
                 onChange={handleChange}
                 className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
               />
               <label className="ml-2 block text-sm font-medium text-gray-700">
                 NLP匹配 (基于正则表达式)
               </label>
             </div>
             
             {rule.nlpMatching && (
               <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                 <div>
                   <label className="block text-sm font-medium text-gray-700 mb-1">
                     正则表达式模式
                   </label>
                   <input
                     type="text"
                     name="nlpRegexPattern"
                     value={rule.nlpRegexPattern}
                     onChange={handleChange}
                     placeholder="例如: 蛋白尿|血尿"
                     className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                   />
                 </div>
                 <div>
                   <label className="block text-sm font-medium text-gray-700 mb-1">
                     NLP匹配得分
                   </label>
                   <input
                     type="number"
                     name="nlpScore"
                     value={rule.nlpScore}
                     onChange={handleChange}
                     min="1"
                     max="20"
                     className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                   />
                 </div>
               </div>
             )}
           </div>
           
           {/* 大模型匹配配置 */}
           <div className={`p-4 bg-purple-50 rounded-lg transition-all duration-300 ${rule.modelRuleMatching ? 'border border-purple-200' : 'border border-dashed border-gray-200'}`}>
             <div className="flex items-center">
               <input
                 type="checkbox"
                 name="modelRuleMatching"
                 checked={rule.modelRuleMatching}
                 onChange={handleChange}
                 className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
               />
               <label className="ml-2 block text-sm font-medium text-gray-700">
                 大模型规则匹配
               </label>
             </div>
             
             {rule.modelRuleMatching && (
               <div className="mt-3 space-y-4">
                 <div>
                   <label className="block text-sm font-medium text-gray-700 mb-1">
                     提示词模板
                   </label>
                   <textarea
                     name="modelPromptTemplate"
                     value={rule.modelPromptTemplate}
                     onChange={handleChange}
                     rows={3}
                     placeholder="判断患者是否有以下症状: {{症状}}。返回1(是)或0(否)。"
                     className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
                   ></textarea>
                 </div>
                 <div>
                   <label className="block text-sm font-medium text-gray-700 mb-1">
                     大模型匹配得分
                   </label>
                   <input
                     type="number"
                     name="modelScore"
                     value={rule.modelScore}
                     onChange={handleChange}
                     min="1"
                     max="20"
                     className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
                   />
                 </div>
               </div>
             )}
           </div>
           
           {/* 数据来源配置 */}
           <div className="p-4 bg-green-50 rounded-lg border border-green-100">
             <label className="block text-sm font-medium text-gray-700 mb-3">
               数据来源配置
             </label>
             
             <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
               <div>
                 <label className="block text-sm font-medium text-gray-700 mb-1">
                   主要数据来源
                 </label>
                 <select
                   name="dataSource"
                   value={rule.dataSource}
                   onChange={handleChange}
                   className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                 >
                   <option value="">请选择数据来源</option>
                   <option value="电子病历">电子病历</option>
                   <option value="实验室检查">实验室检查</option>
                   <option value="影像学检查">影像学检查</option>
                   <option value="家族史记录">家族史记录</option>
                   <option value="基因检测报告">基因检测报告</option>
                   <option value="其他">其他</option>
                 </select>
               </div>
               
               <div className="md:col-span-2">
                 <label className="block text-sm font-medium text-gray-700 mb-1">
                   数据提取SQL
                 </label>
                 <textarea
                   name="dataSourceSql"
                   value={rule.dataSourceSql}
                   onChange={handleChange}
                   rows={2}
                   placeholder="SELECT * FROM medical_records WHERE ..."
                   className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-sm"
                 ></textarea>
               </div>
             </div>
           </div>
           
           <div>
             <label className="block text-sm font-medium text-gray-700 mb-1">
               参考文献
             </label>
             <input
               type="text"
               name="reference"
               value={rule.reference}
               onChange={handleChange}
               placeholder="例如: Niemann M, et al. Ultrasound Med Biol. 2011..."
               className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
             />
           </div>
          
          {!initialRule && (
            <div className="flex items-center">
              <input
                type="checkbox"
                name="isActive"
                checked={rule.isActive}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-700">
                创建后立即启用
              </label>
            </div>
          )}
        </form>
        
        <div className="bg-gray-50 px-6 py-4 flex justify-end space-x-3 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            取消
          </button>
          <button
            type="submit"
            form="rule-form"
            className="px-4 py-2 bg-blue-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {initialRule ? '保存修改' : '创建规则'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default RuleEditorModal;