import React, { useState } from 'react';
import { DiseaseRule } from '@/types';
import { useDiseaseRules } from '@/mocks/diseaseRules';
import RuleEditorModal from './RuleEditorModal';

const DiseaseRules: React.FC = () => {
  const { rules, addRule, updateRule, deleteRule } = useDiseaseRules();
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredRules, setFilteredRules] = useState<DiseaseRule[]>(rules);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentRule, setCurrentRule] = useState<DiseaseRule | null>(null);
  
  // 处理搜索
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toLowerCase();
    setSearchTerm(value);
    
    const filtered = rules.filter(rule => 
      rule.diseaseName.toLowerCase().includes(value) ||
      rule.ruleName.toLowerCase().includes(value) ||
      rule.ruleDescription.toLowerCase().includes(value) ||
      rule.dataSource.toLowerCase().includes(value)
    );
    
    setFilteredRules(filtered);
  };
  
  // 打开添加规则模态框
  const handleAddRule = () => {
    setCurrentRule(null);
    setIsModalOpen(true);
  };
  
  // 打开编辑规则模态框
  const handleEditRule = (rule: DiseaseRule) => {
    setCurrentRule(rule);
    setIsModalOpen(true);
  };
  
  // 保存规则
  const handleSaveRule = (rule: DiseaseRule) => {
    if (currentRule) {
      updateRule(rule);
    } else {
      addRule(rule);
    }
    setIsModalOpen(false);
  };
  
  // 确认删除规则
  const handleDeleteRule = (id: string) => {
    if (window.confirm('确定要删除这条规则吗？此操作不可撤销。')) {
      deleteRule(id);
    }
  };
  
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      <div className="p-5 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-bold text-gray-800 flex items-center">
  <i className="fa-solid fa-list-alt mr-2 text-blue-600"></i>
              疾病风险预测规则
            </h2>
            <p className="text-sm text-gray-500 mt-1">法布雷病风险预测规则定义与管理</p>
          </div>
          <button
            onClick={handleAddRule}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <i className="fa-solid fa-plus mr-2"></i>添加规则
          </button>
        </div>
      </div>
      
      <div className="p-5">
        {/* 搜索栏 */}
        <div className="mb-5 relative">
          <input
            type="text"
            placeholder="搜索规则名称、描述或数据源..."
            value={searchTerm}
            onChange={handleSearch}
            className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-all"
          />
          <i className="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
        </div>
        
        {/* 规则表格 */}
        <div className="overflow-x-auto rounded-lg border border-gray-200">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">病种名称</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则名称</th>
                 <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则描述</th>
                 <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">NLP匹配</th>
                 <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">大模型匹配</th>
                 <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数据来源</th>
                 <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">科室</th>
                 <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">得分</th>
                 <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                 <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
               </tr>
             </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredRules.length > 0 ? (
                filteredRules.map((rule) => (
                  <tr 
                    key={rule.id}
                    className="hover:bg-gray-50 transition-colors"
                  >
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">{rule.diseaseName}</td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{rule.ruleName}</td>
                     <td className="px-4 py-3 text-sm text-gray-500 max-w-xs truncate">{rule.ruleDescription}</td>
                     <td className="px-4 py-3 whitespace-nowrap">
                       {rule.nlpMatching ? (
                         <div className="flex flex-col items-center">
                           <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">已启用</span>
                           <span className="text-xs text-gray-500 mt-1">得分: {rule.nlpScore}</span>
                         </div>
                       ) : (
                         <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">未启用</span>
                       )}
                     </td>
                     <td className="px-4 py-3 whitespace-nowrap">
                       {rule.modelRuleMatching ? (
                         <div className="flex flex-col items-center">
                           <span className="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">已启用</span>
                           <span className="text-xs text-gray-500 mt-1">得分: {rule.modelScore}</span>
                         </div>
                       ) : (
                         <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">未启用</span>
                       )}
                     </td>
                     <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{rule.dataSource}</td>
                     <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{rule.department}</td>
                     <td className="px-4 py-3 whitespace-nowrap">
                       <div className="flex items-center">
                         <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden mr-2">
                           <div 
                             className="h-full bg-gradient-to-r from-blue-500 to-indigo-500"
                             style={{ width: `${Math.min(100, Math.max(0, (rule.nlpScore + rule.modelScore) / 20 * 100))}%` }}
                           ></div>
                         </div>
                         <span className="text-sm font-medium">{rule.nlpScore + rule.modelScore}</span>
                       </div>
                     </td>
                     <td className="px-4 py-3 whitespace-nowrap">
                       <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                         rule.isActive 
                           ? 'bg-green-100 text-green-800' 
                           : 'bg-gray-100 text-gray-800'
                       }`}>
                         {rule.isActive ? '启用' : '禁用'}
                       </span>
                     </td>
                     <td className="px-4 py-3 whitespace-nowrap text-sm font-medium">
                       <div className="flex space-x-2">
                         <button
                           onClick={() => handleEditRule(rule)}
                           className="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-50 transition-colors"
                           title="编辑"
                         >
                           <i className="fa-solid fa-pen-to-square"></i>
                         </button>
                         <button
                           onClick={() => handleDeleteRule(rule.id)}
                           className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50 transition-colors"
                           title="删除"
                         >
                           <i className="fa-solid fa-trash"></i>
                         </button>
                         <button
                           onClick={() => {
                             // 查看规则详情
                             alert(`规则详情: ${rule.ruleName}\n\n${rule.ruleDescription}`);
                           }}
                           className="text-indigo-600 hover:text-indigo-900 p-1 rounded-full hover:bg-indigo-50 transition-colors"
                           title="查看详情"
                         >
                           <i className="fa-solid fa-eye"></i>
                         </button>
                       </div>
                     </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="px-4 py-8 text-center text-sm text-gray-500">
                    没有找到匹配的规则
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      
      {/* 规则编辑模态框 */}
      <RuleEditorModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleSaveRule}
        initialRule={currentRule}
      />
    </div>
  );
};

export default DiseaseRules;