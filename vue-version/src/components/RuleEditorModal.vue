<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="ruleForm"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="loading"
    >
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-form-item label="规则名称" prop="ruleName">
            <el-input
              v-model="formData.ruleName"
              :disabled="isViewMode"
              placeholder="请输入规则名称"
            />
          </el-form-item>
          
          <el-form-item label="规则描述" prop="ruleDescription">
            <el-input
              v-model="formData.ruleDescription"
              :disabled="isViewMode"
              type="textarea"
              :rows="3"
              placeholder="请输入规则描述"
            />
          </el-form-item>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="分类" prop="category">
                <el-select
                  v-model="formData.category"
                  :disabled="isViewMode"
                  placeholder="请选择分类"
                  style="width: 100%"
                >
                  <el-option label="神经系统" value="神经系统" />
                  <el-option label="皮肤系统" value="皮肤系统" />
                  <el-option label="心血管系统" value="心血管系统" />
                  <el-option label="泌尿系统" value="泌尿系统" />
                  <el-option label="眼科系统" value="眼科系统" />
                  <el-option label="耳鼻喉系统" value="耳鼻喉系统" />
                  <el-option label="消化系统" value="消化系统" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="权重" prop="weight">
                <el-input-number
                  v-model="formData.weight"
                  :disabled="isViewMode"
                  :min="1"
                  :max="20"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="科室" prop="department">
                <el-input
                  v-model="formData.department"
                  :disabled="isViewMode"
                  placeholder="请输入科室"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="部位" prop="部位">
                <el-input
                  v-model="formData.部位"
                  :disabled="isViewMode"
                  placeholder="请输入部位"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="状态">
            <el-switch
              v-model="formData.isActive"
              :disabled="isViewMode"
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>
        </el-tab-pane>
        
        <!-- NLP配置 -->
        <el-tab-pane label="NLP配置" name="nlp">
          <el-form-item label="启用NLP匹配">
            <el-switch
              v-model="formData.nlpMatching"
              :disabled="isViewMode"
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>
          
          <template v-if="formData.nlpMatching">
            <el-form-item label="正则表达式" prop="nlpRegexPattern">
              <el-input
                v-model="formData.nlpRegexPattern"
                :disabled="isViewMode"
                placeholder="请输入正则表达式，如：肢端疼痛|感觉异常"
              />
            </el-form-item>
            
            <el-form-item label="NLP得分" prop="nlpScore">
              <el-input-number
                v-model="formData.nlpScore"
                :disabled="isViewMode"
                :min="1"
                :max="20"
                style="width: 200px"
              />
            </el-form-item>
          </template>
        </el-tab-pane>
        
        <!-- 大模型配置 -->
        <el-tab-pane label="大模型配置" name="model">
          <el-form-item label="启用大模型匹配">
            <el-switch
              v-model="formData.modelRuleMatching"
              :disabled="isViewMode"
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>
          
          <template v-if="formData.modelRuleMatching">
            <el-form-item label="提示词模板" prop="modelPromptTemplate">
              <el-input
                v-model="formData.modelPromptTemplate"
                :disabled="isViewMode"
                type="textarea"
                :rows="4"
                placeholder="请输入大模型提示词模板，使用{{变量名}}表示占位符"
              />
            </el-form-item>
            
            <el-form-item label="模型得分" prop="modelScore">
              <el-input-number
                v-model="formData.modelScore"
                :disabled="isViewMode"
                :min="1"
                :max="20"
                style="width: 200px"
              />
            </el-form-item>
          </template>
        </el-tab-pane>
        
        <!-- 数据源配置 -->
        <el-tab-pane label="数据源配置" name="datasource">
          <el-form-item label="数据来源" prop="dataSource">
            <el-input
              v-model="formData.dataSource"
              :disabled="isViewMode"
              placeholder="如：电子病历、实验室检查等"
            />
          </el-form-item>
          
          <el-form-item label="系统来源" prop="systemSource">
            <el-input
              v-model="formData.systemSource"
              :disabled="isViewMode"
              placeholder="如：病历文书、检验报告等"
            />
          </el-form-item>
          
          <el-form-item label="SQL查询语句" prop="dataSourceSql">
            <el-input
              v-model="formData.dataSourceSql"
              :disabled="isViewMode"
              type="textarea"
              :rows="4"
              placeholder="请输入数据查询SQL语句"
            />
          </el-form-item>
          
          <el-form-item label="参考文献" prop="reference">
            <el-input
              v-model="formData.reference"
              :disabled="isViewMode"
              placeholder="请输入参考文献"
            />
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        v-if="!isViewMode"
        type="primary"
        @click="handleSave"
        :loading="loading"
      >
        保存
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'RuleEditorModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rule: {
      type: Object,
      default: null
    },
    mode: {
      type: String,
      default: 'view' // 'view', 'edit', 'add'
    }
  },
  data() {
    return {
      activeTab: 'basic',
      loading: false,
      formData: {
        id: '',
        diseaseName: '法布雷病',
        ruleName: '',
        ruleDescription: '',
        nlpMatching: false,
        nlpRegexPattern: '',
        nlpScore: 5,
        modelRuleMatching: false,
        modelPromptTemplate: '',
        modelScore: 5,
        dataSource: '',
        dataSourceSql: '',
        department: '',
        systemSource: '',
        部位: '',
        createdAt: '',
        isActive: true,
        weight: 5,
        category: '',
        reference: ''
      },
      formRules: {
        ruleName: [
          { required: true, message: '请输入规则名称', trigger: 'blur' }
        ],
        ruleDescription: [
          { required: true, message: '请输入规则描述', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择分类', trigger: 'change' }
        ],
        weight: [
          { required: true, message: '请输入权重', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    
    dialogTitle() {
      const titleMap = {
        view: '查看规则',
        edit: '编辑规则',
        add: '新增规则'
      }
      return titleMap[this.mode] || '规则详情'
    },
    
    isViewMode() {
      return this.mode === 'view'
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initFormData()
      }
    }
  },
  methods: {
    initFormData() {
      if (this.rule) {
        this.formData = { ...this.rule }
      } else {
        // 重置表单数据
        this.formData = {
          id: '',
          diseaseName: '法布雷病',
          ruleName: '',
          ruleDescription: '',
          nlpMatching: false,
          nlpRegexPattern: '',
          nlpScore: 5,
          modelRuleMatching: false,
          modelPromptTemplate: '',
          modelScore: 5,
          dataSource: '',
          dataSourceSql: '',
          department: '',
          systemSource: '',
          部位: '',
          createdAt: new Date().toISOString(),
          isActive: true,
          weight: 5,
          category: '',
          reference: ''
        }
      }
      
      // 重置表单验证
      this.$nextTick(() => {
        if (this.$refs.ruleForm) {
          this.$refs.ruleForm.clearValidate()
        }
      })
    },
    
    handleClose() {
      this.dialogVisible = false
      this.activeTab = 'basic'
    },
    
    handleSave() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 生成ID（如果是新增）
          if (this.mode === 'add' && !this.formData.id) {
            this.formData.id = 'DR' + Date.now().toString().slice(-6)
          }
          
          this.$emit('save', { ...this.formData })
        } else {
          this.$message.error('请完善必填信息')
        }
      })
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.el-tabs--border-card {
  border: none;
  box-shadow: none;
}

.el-tab-pane {
  padding: 20px;
}
</style>
