import React from 'react';
import { useNavigate } from 'react-router-dom';
import RiskScoreVisualization from '@/components/RiskScoreVisualization';
import { patients } from '@/mocks/patients';
import { diseaseRules } from '@/mocks/diseaseRules';

// 计算风险分布统计
const calculateRiskDistribution = () => {
  const highRisk = patients.filter(p => p.riskScore >= 70).length;
  const mediumRisk = patients.filter(p => p.riskScore >= 30 && p.riskScore < 70).length;
  const lowRisk = patients.filter(p => p.riskScore < 30).length;
  
  return { highRisk, mediumRisk, lowRisk };
};

// 计算平均风险得分
const calculateAverageRiskScore = () => {
  const sum = patients.reduce((acc, patient) => acc + patient.riskScore, 0);
  return Math.round(sum / patients.length);
};

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { highRisk, mediumRisk, lowRisk } = calculateRiskDistribution();
  const averageScore = calculateAverageRiskScore();
  const activeRulesCount = diseaseRules.filter(rule => rule.isActive).length;
  
  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      {/* 页面标题 */}
      <div className="text-center mb-10">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          疾病风险预测系统
        </h1>
        <p className="text-xl text-gray-600 font-medium">法布雷病专项版</p>
        <div className="mt-4 inline-flex items-center px-4 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
          <i className="fa-solid fa-calendar-check mr-2"></i>
          数据更新时间: {new Date().toLocaleDateString()}
        </div>
      </div>
      
      {/* 统计概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* 平均风险得分 */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-5 transform transition-all hover:shadow-md hover:-translate-y-1">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">平均风险得分</p>
              <h3 className="text-2xl font-bold text-gray-900 mt-1">{averageScore}</h3>
              <p className="text-xs text-gray-500 mt-1">基于{patients.length}例患者数据</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <RiskScoreVisualization score={averageScore} size={80} />
            </div>
          </div>
        </div>
        
        {/* 高风险患者 */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-5 transform transition-all hover:shadow-md hover:-translate-y-1">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">高风险患者</p>
              <h3 className="text-2xl font-bold text-red-600 mt-1">{highRisk}</h3>
              <p className="text-xs text-gray-500 mt-1">需重点关注</p>
            </div>
            <div className="p-3 bg-red-50 rounded-lg">
              <i className="fa-solid fa-exclamation-triangle text-2xl text-red-500"></i>
            </div>
          </div>
        </div>
        
        {/* 总评估患者 */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-5 transform transition-all hover:shadow-md hover:-translate-y-1">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">总评估患者</p>
              <h3 className="text-2xl font-bold text-gray-900 mt-1">{patients.length}</h3>
              <p className="text-xs text-gray-500 mt-1">
                中风险: {mediumRisk} | 低风险: {lowRisk}
              </p>
            </div>
            <div className="p-3 bg-indigo-50 rounded-lg">
              <i className="fa-solid fa-users text-2xl text-indigo-500"></i>
            </div>
          </div>
        </div>
        
        {/* 活跃规则数 */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-5 transform transition-all hover:shadow-md hover:-translate-y-1">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">活跃预测规则</p>
              <h3 className="text-2xl font-bold text-green-600 mt-1">{activeRulesCount}</h3>
              <p className="text-xs text-gray-500 mt-1">总计: {diseaseRules.length}条规则</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <i className="fa-solid fa-list-checks text-2xl text-green-500"></i>
            </div>
          </div>
        </div>
      </div>
      
      {/* 快速访问卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl shadow-sm border border-blue-100 p-6 transform transition-all hover:shadow-md hover:-translate-y-1 cursor-pointer">
          <div className="flex items-start">
            <div className="p-3 bg-blue-100 rounded-lg mr-4">
  <i className="fa-solid fa-list-alt text-2xl text-blue-600"></i>
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900">疾病风险预测规则</h3>
              <p className="text-gray-600 mt-2">管理和配置法布雷病风险预测规则，设置匹配条件和权重。</p>
             <button 
               onClick={() => navigate('/rules')}
               className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
             >
               管理规则 <i className="fa-solid fa-arrow-right ml-2"></i>
             </button>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-indigo-50 to-purple-100 rounded-xl shadow-sm border border-indigo-100 p-6 transform transition-all hover:shadow-md hover:-translate-y-1 cursor-pointer">
          <div className="flex items-start">
            <div className="p-3 bg-indigo-100 rounded-lg mr-4">
              <i className="fa-solid fa-user-md text-2xl text-indigo-600"></i>
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900">患者风险标记</h3>
              <p className="text-gray-600 mt-2">查看患者风险评估结果，分析各项得分明细和匹配规则详情。</p>
             <button 
               onClick={() => navigate('/patients')}
               className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
             >
               查看患者 <i className="fa-solid fa-arrow-right ml-2"></i>
             </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* 系统说明 */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-3">系统说明</h3>
        <p className="text-gray-600">
          本系统用于法布雷病风险预测和评估，通过多维度规则匹配算法对患者风险进行评分。
          系统提供风险规则管理和患者风险标记功能，帮助医护人员快速识别高风险患者。
        </p>
      </div>
    </div>
  );
};

export default Home;