export interface DiseaseRule {
  id: string;
  diseaseName: string;
  ruleName: string;
  ruleDescription: string;
  // NLP相关配置
  nlpMatching: boolean;
  nlpRegexPattern: string; // NLP正则表达式
  nlpScore: number; // NLP匹配得分
  
  // 大模型相关配置
  modelRuleMatching: boolean;
  modelPromptTemplate: string; // 大模型提示词模板
  modelScore: number; // 大模型匹配得分
  
  dataSource: string;
  dataSourceSql: string; // 数据来源SQL语句
  department: string; // 科室来源
  systemSource: string; // 系统来源
  部位: string; // 部位
  
  createdAt: string;
  isActive: boolean;
  weight: number; // 规则权重，用于计算得分
  category: string; // 规则分类
  reference: string; // 参考文献
}

export interface ScoringItem {
  id: string;
  ruleId: string;
  ruleName: string;
  score: number;
  maxScore: number;
  isMatched: boolean;
  matchedDetails: string; // 匹配详情
  sourceData: string; // 来源数据
  referenceValue: string; // 参考值范围
  description: string;
}

export interface Patient {
  id: string;
  patientName: string; // 脱敏姓名
  patientId: string;
  gender: 'male' | 'female' | 'other';
  riskDisease: string;
  riskScore: number;
  scoringItems: ScoringItem[];
  lastPredictionTime: string;
}

export enum RiskLevel {
  LOW = '低风险',
  MEDIUM = '中风险',
  HIGH = '高风险',
}

export interface RiskPrediction {
  diseaseName: string;
  riskScore: number;
  riskLevel: RiskLevel;
  predictionTime: string;
}