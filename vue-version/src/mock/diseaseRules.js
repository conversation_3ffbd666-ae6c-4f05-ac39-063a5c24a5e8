export const diseaseRules = [
  {
    id: 'DR001',
    diseaseName: '法布雷病',
    ruleName: '神经性疼痛（肢端皮肤疼痛）',
    ruleDescription: '患者存在肢端疼痛、感觉异常、血管角质瘤、角膜涡状混浊等症状',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: '肢端疼痛|感觉异常|血管角质瘤|角膜涡状混浊',
    nlpScore: 5,
    // 大模型相关配置
    modelRuleMatching: false,
    modelPromptTemplate: '',
    modelScore: 0,
    dataSource: '电子病历',
    dataSourceSql: 'SELECT * FROM medical_records WHERE chief_complaint REGEXP "肢端疼痛|感觉异常"',
    department: '神经内科',
    systemSource: '病历文书',
    部位: '周围神经',
    createdAt: '2025-06-15T09:30:00Z',
    isActive: true,
    weight: 5,
    category: '神经系统',
    reference: 'Yo<PERSON>da S, et al. Orphanet J Rare Dis. 2020 Aug 26;15(1):220.'
  },
  {
    id: 'DR002',
    diseaseName: '法布雷病',
    ruleName: '皮肤血管角质瘤',
    ruleDescription: '皮肤出现典型的血管角质瘤表现',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: '皮肤血管角质瘤|血管角质瘤',
    nlpScore: 10,
    // 大模型相关配置
    modelRuleMatching: true,
    modelPromptTemplate: '分析以下病历内容，判断患者是否有皮肤血管角质瘤表现: {{病历文本}}。返回1(是)或0(否)。',
    modelScore: 10,
    dataSource: '电子病历',
    dataSourceSql: 'SELECT * FROM medical_records WHERE diagnosis LIKE "%血管角质瘤%"',
    department: '皮肤科',
    systemSource: '病历文书、诊断',
    部位: '皮肤',
    createdAt: '2025-06-15T09:35:00Z',
    isActive: true,
    weight: 10,
    category: '皮肤系统',
    reference: 'Yoshida S, et al. Orphanet J Rare Dis. 2020 Aug 26;15(1):220.'
  },
  {
    id: 'DR003',
    diseaseName: '法布雷病',
    ruleName: '角膜涡状混浊',
    ruleDescription: '眼科检查发现角膜涡状混浊',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: '角膜涡状混浊|角膜混浊',
    nlpScore: 8,
    // 大模型相关配置
    modelRuleMatching: false,
    modelPromptTemplate: '',
    modelScore: 0,
    dataSource: '电子病历',
    dataSourceSql: 'SELECT * FROM medical_records WHERE examination LIKE "%角膜%混浊%"',
    department: '眼科',
    systemSource: '检查报告',
    部位: '眼部',
    createdAt: '2025-06-15T09:40:00Z',
    isActive: true,
    weight: 8,
    category: '眼科系统',
    reference: 'Yoshida S, et al. Orphanet J Rare Dis. 2020 Aug 26;15(1):220.'
  },
  {
    id: 'DR004',
    diseaseName: '法布雷病',
    ruleName: '心脏肥厚',
    ruleDescription: '超声心动图显示左心室肥厚',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: '左心室肥厚|心脏肥厚|心肌肥厚',
    nlpScore: 12,
    // 大模型相关配置
    modelRuleMatching: true,
    modelPromptTemplate: '分析以下超声心动图报告，判断是否存在左心室肥厚: {{报告内容}}。返回1(是)或0(否)。',
    modelScore: 12,
    dataSource: '超声心动图',
    dataSourceSql: 'SELECT * FROM echo_reports WHERE findings LIKE "%左心室肥厚%" OR findings LIKE "%心肌肥厚%"',
    department: '心内科',
    systemSource: '检查报告',
    部位: '心脏',
    createdAt: '2025-06-15T09:45:00Z',
    isActive: true,
    weight: 12,
    category: '心血管系统',
    reference: 'Yoshida S, et al. Orphanet J Rare Dis. 2020 Aug 26;15(1):220.'
  },
  {
    id: 'DR005',
    diseaseName: '法布雷病',
    ruleName: '肾功能异常',
    ruleDescription: '血清肌酐升高或蛋白尿',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: '血清肌酐升高|蛋白尿|肾功能异常',
    nlpScore: 10,
    // 大模型相关配置
    modelRuleMatching: false,
    modelPromptTemplate: '',
    modelScore: 0,
    dataSource: '实验室检查',
    dataSourceSql: 'SELECT * FROM lab_results WHERE creatinine > 1.2 OR proteinuria = 1',
    department: '肾内科',
    systemSource: '检验报告',
    部位: '肾脏',
    createdAt: '2025-06-15T09:50:00Z',
    isActive: true,
    weight: 10,
    category: '泌尿系统',
    reference: 'Yoshida S, et al. Orphanet J Rare Dis. 2020 Aug 26;15(1):220.'
  },
  {
    id: 'DR006',
    diseaseName: '法布雷病',
    ruleName: '听力下降',
    ruleDescription: '听力检查显示感音神经性听力下降',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: '听力下降|感音神经性听力|听力异常',
    nlpScore: 6,
    // 大模型相关配置
    modelRuleMatching: false,
    modelPromptTemplate: '',
    modelScore: 0,
    dataSource: '听力检查',
    dataSourceSql: 'SELECT * FROM hearing_tests WHERE hearing_loss = 1',
    department: '耳鼻喉科',
    systemSource: '检查报告',
    部位: '耳部',
    createdAt: '2025-06-15T09:55:00Z',
    isActive: true,
    weight: 6,
    category: '耳鼻喉系统',
    reference: 'Yoshida S, et al. Orphanet J Rare Dis. 2020 Aug 26;15(1):220.'
  },
  {
    id: 'DR007',
    diseaseName: '法布雷病',
    ruleName: '脑血管病变',
    ruleDescription: 'MRI显示脑白质病变或脑血管异常',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: '脑白质病变|脑血管异常|脑血管病变',
    nlpScore: 15,
    // 大模型相关配置
    modelRuleMatching: true,
    modelPromptTemplate: '分析以下脑部MRI报告，判断是否存在脑白质病变或脑血管异常: {{MRI报告}}。返回1(是)或0(否)。',
    modelScore: 15,
    dataSource: '脑部MRI',
    dataSourceSql: 'SELECT * FROM mri_reports WHERE findings LIKE "%脑白质%" OR findings LIKE "%脑血管%"',
    department: '神经内科',
    systemSource: '影像报告',
    部位: '脑部',
    createdAt: '2025-06-15T10:00:00Z',
    isActive: true,
    weight: 15,
    category: '神经系统',
    reference: 'Yoshida S, et al. Orphanet J Rare Dis. 2020 Aug 26;15(1):220.'
  },
  {
    id: 'DR008',
    diseaseName: '法布雷病',
    ruleName: '胃肠道症状',
    ruleDescription: '腹痛、腹泻等胃肠道症状',
    // NLP相关配置
    nlpMatching: true,
    nlpRegexPattern: '腹痛|腹泻|胃肠道症状',
    nlpScore: 4,
    // 大模型相关配置
    modelRuleMatching: false,
    modelPromptTemplate: '',
    modelScore: 0,
    dataSource: '电子病历',
    dataSourceSql: 'SELECT * FROM medical_records WHERE symptoms LIKE "%腹痛%" OR symptoms LIKE "%腹泻%"',
    department: '消化内科',
    systemSource: '病历文书',
    部位: '胃肠道',
    createdAt: '2025-06-15T10:05:00Z',
    isActive: true,
    weight: 4,
    category: '消化系统',
    reference: 'Yoshida S, et al. Orphanet J Rare Dis. 2020 Aug 26;15(1):220.'
  }
]
