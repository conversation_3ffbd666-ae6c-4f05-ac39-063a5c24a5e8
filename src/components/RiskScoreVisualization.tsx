import React from 'react';
import { RiskLevel } from '@/types';

interface RiskScoreVisualizationProps {
  score: number;
  size?: number;
}

const RiskScoreVisualization: React.FC<RiskScoreVisualizationProps> = ({ 
  score, 
  size = 120 
}) => {
  // 确定风险等级和颜色
  let level: RiskLevel;
  let color: string;
  
  if (score >= 70) {
    level = RiskLevel.HIGH;
    color = 'from-red-500 to-red-600';
  } else if (score >= 30) {
    level = RiskLevel.MEDIUM;
    color = 'from-yellow-500 to-yellow-600';
  } else {
    level = RiskLevel.LOW;
    color = 'from-green-500 to-green-600';
  }
  
  const circumference = size * 0.8 * Math.PI;
  const strokeDashoffset = circumference - (score / 100) * circumference;
  
  return (
    <div className="flex flex-col items-center justify-center">
      <div className="relative">
        {/* 背景圆环 */}
        <svg width={size} height={size} className="transform -rotate-90">
          <circle
            cx={size / 2}
            cy={size / 2}
            r={size * 0.4}
            fill="none"
            stroke="#e5e7eb"
            strokeWidth={size * 0.08}
          />
          {/* 得分圆环 */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={size * 0.4}
            fill="none"
            stroke={`url(#gradient-${score})`}
            strokeWidth={size * 0.08}
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-1000 ease-out"
          />
          {/* 渐变定义 */}
          <defs>
            <linearGradient id={`gradient-${score}`}>
              <stop offset="0%" className={color.split(' ')[0]} />
              <stop offset="100%" className={color.split(' ')[1]} />
            </linearGradient>
          </defs>
        </svg>
        {/* 中心得分 */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <span className="text-2xl font-bold">{score}</span>
          <span className="text-xs text-gray-500">风险评分</span>
        </div>
      </div>
      <span className="mt-2 text-sm font-medium capitalize" 
        style={{ 
          color: score >= 70 ? '#ef4444' : score >= 30 ? '#f59e0b' : '#10b981' 
        }}>
        {level}
      </span>
    </div>
  );
};

export default RiskScoreVisualization;