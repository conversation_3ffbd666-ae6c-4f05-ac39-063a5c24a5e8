# 快速开始

## 安装依赖

```bash
cd vue-version
npm install
```

## 启动开发服务器

```bash
npm run serve
```

访问 http://localhost:3001

## 项目特点

1. **Vue2 + Element UI** - 成熟稳定的技术栈
2. **完整功能** - 包含规则管理、患者管理等完整功能
3. **响应式设计** - 支持移动端和桌面端
4. **数据可视化** - 使用ECharts进行图表展示
5. **模块化架构** - 清晰的项目结构和组件划分

## 主要页面

- **首页** (`/`) - 系统概览和统计数据
- **规则管理** (`/rules`) - 疾病风险预测规则的增删改查
- **患者管理** (`/patients`) - 患者风险评估结果查看

## 技术亮点

- 使用Vuex进行状态管理
- Element UI提供丰富的组件
- ECharts实现数据可视化
- 响应式布局适配各种设备
- 模拟数据支持完整的功能演示
