import React, { useState } from 'react';
import { Patient } from '@/types';
import { patients } from '@/mocks/patients';
import PatientDetailsModal from './PatientDetailsModal';

const PatientTagging: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof Patient | null>('riskScore');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>(patients);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  
  // 处理搜索
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toLowerCase();
    setSearchTerm(value);
    
    filterAndSortPatients(value);
  };
  
  // 处理排序
  const handleSort = (field: keyof Patient) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
    
    filterAndSortPatients(searchTerm);
  };
  
  // 筛选和排序患者数据
  const filterAndSortPatients = (searchValue: string) => {
    let result = [...patients];
    
    // 筛选
    if (searchValue) {
      result = result.filter(patient => 
        patient.patientName.toLowerCase().includes(searchValue) ||
        patient.patientId.toLowerCase().includes(searchValue) ||
        patient.riskDisease.toLowerCase().includes(searchValue)
      );
    }
    
    // 排序
    if (sortField) {
      result.sort((a, b) => {
        // 处理不同类型的排序
        const valueA = a[sortField];
        const valueB = b[sortField];
        
        if (typeof valueA === 'string' && typeof valueB === 'string') {
          return sortDirection === 'asc' 
            ? valueA.localeCompare(valueB) 
            : valueB.localeCompare(valueA);
        }
        
        if (typeof valueA === 'number' && typeof valueB === 'number') {
          return sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
        }
        
        return 0;
      });
    }
    
    setFilteredPatients(result);
  };
  
  // 获取排序图标
  const getSortIcon = (field: keyof Patient) => {
    if (sortField !== field) return null;
    
    return sortDirection === 'asc' ? (
      <i className="fa-solid fa-sort-up ml-1 text-xs text-gray-400"></i>
    ) : (
      <i className="fa-solid fa-sort-down ml-1 text-xs text-gray-400"></i>
    );
  };
  
  // 查看患者详情
  const viewPatientDetails = (patient: Patient) => {
    setSelectedPatient(patient);
    setIsDetailsOpen(true);
  };
  
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      <div className="p-5 border-b border-gray-100 bg-gradient-to-r from-indigo-50 to-purple-50">
        <h2 className="text-xl font-bold text-gray-800 flex items-center">
          <i className="fa-solid fa-user-md mr-2 text-indigo-600"></i>患者风险标记
        </h2>
        <p className="text-sm text-gray-500 mt-1">已筛选患者风险预测结果</p>
      </div>
      
      <div className="p-5">
        {/* 搜索栏 */}
        <div className="mb-5 relative">
          <input
            type="text"
            placeholder="搜索患者姓名、编号或病种..."
            value={searchTerm}
            onChange={handleSearch}
            className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 focus:outline-none transition-all"
          />
          <i className="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
        </div>
        
        {/* 患者表格 */}
        <div className="overflow-x-auto rounded-lg border border-gray-200">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">患者信息</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预测病种</th>
                <th 
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('riskScore')}
                >
                  风险评分 {getSortIcon('riskScore')}
                </th>
                <th 
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('lastPredictionTime')}
                >
                  最后评估时间 {getSortIcon('lastPredictionTime')}
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPatients.length > 0 ? (
                filteredPatients.map((patient) => (
                  <tr 
                    key={patient.id}
                    className="hover:bg-gray-50 transition-colors"
                  >
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{patient.patientName}</div>
                        <div className="text-xs text-gray-500">{patient.patientId}</div>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        patient.gender === 'male' 
                          ? 'bg-blue-100 text-blue-800' 
                          : 'bg-pink-100 text-pink-800'
                      }`}>
                        {patient.gender === 'male' ? '男' : '女'}
                      </span>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{patient.riskDisease}</td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden mr-2">
                          <div 
                            className={`h-full ${
                              patient.riskScore >= 70 
                                ? 'bg-red-500' 
                                : patient.riskScore >= 30 
                                  ? 'bg-yellow-500' 
                                  : 'bg-green-500'
                            } transition-all duration-700 ease-out`}
                            style={{ width: `${patient.riskScore}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">{patient.riskScore}</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{patient.lastPredictionTime}</td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => viewPatientDetails(patient)}
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        <i className="fa-solid fa-eye mr-1"></i>查看详情
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="px-4 py-8 text-center text-sm text-gray-500">
                    没有找到匹配的患者
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      
      {/* 患者详情模态框 */}
      <PatientDetailsModal
        isOpen={isDetailsOpen}
        onClose={() => setIsDetailsOpen(false)}
        patient={selectedPatient}
      />
    </div>
  );
};

export default PatientTagging;