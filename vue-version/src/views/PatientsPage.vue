<template>
  <div class="patients-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">患者风险标记管理</h1>
      <p class="page-subtitle">查看和分析患者风险评估结果</p>
    </div>
    
    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索患者姓名或ID"
          prefix-icon="el-icon-search"
          style="width: 250px;"
          clearable
        />
        <el-select
          v-model="filterRiskLevel"
          placeholder="筛选风险等级"
          style="width: 150px; margin-left: 12px;"
          clearable
        >
          <el-option label="全部风险" value="" />
          <el-option label="高风险" value="high" />
          <el-option label="中风险" value="medium" />
          <el-option label="低风险" value="low" />
        </el-select>
        <el-select
          v-model="filterGender"
          placeholder="筛选性别"
          style="width: 120px; margin-left: 12px;"
          clearable
        >
          <el-option label="全部性别" value="" />
          <el-option label="男性" value="male" />
          <el-option label="女性" value="female" />
        </el-select>
      </div>
      <div class="action-right">
        <el-button type="primary" icon="el-icon-refresh" @click="refreshData">
          刷新数据
        </el-button>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="8" :lg="6">
        <el-card class="stats-card high-risk" shadow="hover">
          <div class="stats-content">
            <div class="stats-info">
              <p class="stats-label">高风险患者</p>
              <h3 class="stats-value">{{ highRiskCount }}</h3>
            </div>
            <div class="stats-icon danger">
              <i class="fa-solid fa-exclamation-triangle"></i>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="8" :lg="6">
        <el-card class="stats-card medium-risk" shadow="hover">
          <div class="stats-content">
            <div class="stats-info">
              <p class="stats-label">中风险患者</p>
              <h3 class="stats-value">{{ mediumRiskCount }}</h3>
            </div>
            <div class="stats-icon warning">
              <i class="fa-solid fa-exclamation-circle"></i>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="8" :lg="6">
        <el-card class="stats-card low-risk" shadow="hover">
          <div class="stats-content">
            <div class="stats-info">
              <p class="stats-label">低风险患者</p>
              <h3 class="stats-value">{{ lowRiskCount }}</h3>
            </div>
            <div class="stats-icon success">
              <i class="fa-solid fa-check-circle"></i>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="6">
        <el-card class="stats-card total" shadow="hover">
          <div class="stats-content">
            <div class="stats-info">
              <p class="stats-label">总患者数</p>
              <h3 class="stats-value">{{ totalPatients }}</h3>
            </div>
            <div class="stats-icon primary">
              <i class="fa-solid fa-users"></i>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 患者列表 -->
    <el-card class="patients-table-card" shadow="hover">
      <el-table
        :data="paginatedPatients"
        v-loading="loading"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="patientId" label="患者ID" width="120" />
        <el-table-column prop="patientName" label="患者姓名" width="120" />
        <el-table-column prop="gender" label="性别" width="80">
          <template slot-scope="scope">
            <el-tag size="small" :type="getGenderType(scope.row.gender)">
              {{ getGenderText(scope.row.gender) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="riskScore" label="风险得分" width="120" sortable>
          <template slot-scope="scope">
            <div class="risk-score-cell">
              <RiskScoreVisualization :score="scope.row.riskScore" :size="40" />
              <span class="score-text">{{ scope.row.riskScore }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="riskLevel" label="风险等级" width="120">
          <template slot-scope="scope">
            <el-tag
              size="medium"
              :type="getRiskLevelType(scope.row.riskScore)"
            >
              {{ getRiskLevel(scope.row.riskScore) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastPredictionTime" label="最后预测时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.lastPredictionTime) }}
          </template>
        </el-table-column>
        <el-table-column label="匹配规则数" width="120">
          <template slot-scope="scope">
            <span>{{ getMatchedRulesCount(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-view"
              @click="viewPatientDetails(scope.row)"
            >
              查看详情
            </el-button>
            <el-button
              size="mini"
              type="warning"
              icon="el-icon-edit"
              @click="editPatient(scope.row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredPatients.length"
        />
      </div>
    </el-card>
    
    <!-- 患者详情对话框 -->
    <PatientDetailsModal
      :visible.sync="detailsDialogVisible"
      :patient="currentPatient"
    />
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { getRiskLevel, getRiskLevelType } from '@/types'
import RiskScoreVisualization from '@/components/RiskScoreVisualization.vue'
import PatientDetailsModal from '@/components/PatientDetailsModal.vue'

export default {
  name: 'PatientsPage',
  components: {
    RiskScoreVisualization,
    PatientDetailsModal
  },
  data() {
    return {
      searchKeyword: '',
      filterRiskLevel: '',
      filterGender: '',
      currentPage: 1,
      pageSize: 20,
      detailsDialogVisible: false,
      currentPatient: null,
      loading: false
    }
  },
  computed: {
    ...mapState(['patients']),
    ...mapGetters(['highRiskPatients', 'mediumRiskPatients', 'lowRiskPatients']),
    
    highRiskCount() {
      return this.highRiskPatients.length
    },
    
    mediumRiskCount() {
      return this.mediumRiskPatients.length
    },
    
    lowRiskCount() {
      return this.lowRiskPatients.length
    },
    
    totalPatients() {
      return this.patients.length
    },
    
    filteredPatients() {
      let patients = [...this.patients]
      
      // 搜索过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        patients = patients.filter(patient =>
          patient.patientName.toLowerCase().includes(keyword) ||
          patient.patientId.toLowerCase().includes(keyword)
        )
      }
      
      // 风险等级过滤
      if (this.filterRiskLevel) {
        patients = patients.filter(patient => {
          const riskLevel = this.getRiskLevelKey(patient.riskScore)
          return riskLevel === this.filterRiskLevel
        })
      }
      
      // 性别过滤
      if (this.filterGender) {
        patients = patients.filter(patient => patient.gender === this.filterGender)
      }
      
      return patients
    },
    
    paginatedPatients() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredPatients.slice(start, end)
    }
  },
  methods: {
    getRiskLevel,
    getRiskLevelType,
    
    getRiskLevelKey(score) {
      if (score >= 70) return 'high'
      if (score >= 30) return 'medium'
      return 'low'
    },
    
    getGenderText(gender) {
      const genderMap = {
        male: '男',
        female: '女',
        other: '其他'
      }
      return genderMap[gender] || '未知'
    },
    
    getGenderType(gender) {
      const typeMap = {
        male: 'primary',
        female: 'success',
        other: 'info'
      }
      return typeMap[gender] || ''
    },
    
    getMatchedRulesCount(patient) {
      return patient.scoringItems.filter(item => item.isMatched).length
    },
    
    formatDate(dateString) {
      return new Date(dateString).toLocaleString('zh-CN')
    },
    
    handleSortChange({ column, prop, order }) {
      // 处理排序逻辑
      console.log('Sort change:', { column, prop, order })
    },
    
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
    },
    
    refreshData() {
      this.loading = true
      // 模拟刷新数据
      setTimeout(() => {
        this.loading = false
        this.$message.success('数据刷新成功')
      }, 1000)
    },
    
    viewPatientDetails(patient) {
      this.currentPatient = patient
      this.detailsDialogVisible = true
    },
    
    editPatient(patient) {
      this.$message.info('编辑患者功能开发中...')
    }
  }
}
</script>

<style scoped>
.patients-page {
  padding: 20px 0;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 16px;
  color: #606266;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.action-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.stats-row {
  margin-bottom: 30px;
}

.stats-card {
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.stats-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-info {
  flex: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-left: 16px;
}

.stats-icon.danger {
  background: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

.stats-icon.warning {
  background: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.stats-icon.success {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.stats-icon.primary {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.patients-table-card {
  margin-bottom: 20px;
}

.risk-score-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-text {
  font-weight: bold;
  color: #303133;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-left {
    justify-content: center;
  }
  
  .action-right {
    text-align: center;
  }
  
  .pagination-wrapper {
    text-align: center;
  }
  
  .stats-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-icon {
    margin: 16px 0 0 0;
  }
}
</style>
