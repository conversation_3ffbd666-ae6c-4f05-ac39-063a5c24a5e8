import Vue from 'vue'
import Vuex from 'vuex'
import { diseaseRules } from '@/mock/diseaseRules'
import { patients } from '@/mock/patients'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    // 用户认证状态
    isAuthenticated: true,
    
    // 疾病规则数据
    diseaseRules: [...diseaseRules],
    
    // 患者数据
    patients: [...patients],
    
    // 加载状态
    loading: false
  },
  
  getters: {
    // 获取活跃的规则
    activeRules: state => {
      return state.diseaseRules.filter(rule => rule.isActive)
    },
    
    // 获取高风险患者
    highRiskPatients: state => {
      return state.patients.filter(patient => patient.riskScore >= 70)
    },
    
    // 获取中风险患者
    mediumRiskPatients: state => {
      return state.patients.filter(patient => patient.riskScore >= 30 && patient.riskScore < 70)
    },
    
    // 获取低风险患者
    lowRiskPatients: state => {
      return state.patients.filter(patient => patient.riskScore < 30)
    },
    
    // 计算平均风险得分
    averageRiskScore: state => {
      if (state.patients.length === 0) return 0
      const sum = state.patients.reduce((acc, patient) => acc + patient.riskScore, 0)
      return Math.round(sum / state.patients.length)
    },
    
    // 获取风险分布统计
    riskDistribution: (state, getters) => {
      return {
        high: getters.highRiskPatients.length,
        medium: getters.mediumRiskPatients.length,
        low: getters.lowRiskPatients.length
      }
    }
  },
  
  mutations: {
    // 设置认证状态
    SET_AUTHENTICATED(state, status) {
      state.isAuthenticated = status
    },
    
    // 设置加载状态
    SET_LOADING(state, status) {
      state.loading = status
    },
    
    // 添加新规则
    ADD_RULE(state, rule) {
      state.diseaseRules.push(rule)
    },
    
    // 更新规则
    UPDATE_RULE(state, updatedRule) {
      const index = state.diseaseRules.findIndex(rule => rule.id === updatedRule.id)
      if (index !== -1) {
        Vue.set(state.diseaseRules, index, updatedRule)
      }
    },
    
    // 删除规则
    DELETE_RULE(state, ruleId) {
      state.diseaseRules = state.diseaseRules.filter(rule => rule.id !== ruleId)
    },
    
    // 更新患者数据
    UPDATE_PATIENT(state, updatedPatient) {
      const index = state.patients.findIndex(patient => patient.id === updatedPatient.id)
      if (index !== -1) {
        Vue.set(state.patients, index, updatedPatient)
      }
    }
  },
  
  actions: {
    // 登出
    logout({ commit }) {
      commit('SET_AUTHENTICATED', false)
    },
    
    // 添加规则
    async addRule({ commit }, rule) {
      commit('SET_LOADING', true)
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        commit('ADD_RULE', rule)
        return { success: true }
      } catch (error) {
        return { success: false, error: error.message }
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    // 更新规则
    async updateRule({ commit }, rule) {
      commit('SET_LOADING', true)
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        commit('UPDATE_RULE', rule)
        return { success: true }
      } catch (error) {
        return { success: false, error: error.message }
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    // 删除规则
    async deleteRule({ commit }, ruleId) {
      commit('SET_LOADING', true)
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        commit('DELETE_RULE', ruleId)
        return { success: true }
      } catch (error) {
        return { success: false, error: error.message }
      } finally {
        commit('SET_LOADING', false)
      }
    }
  }
})
