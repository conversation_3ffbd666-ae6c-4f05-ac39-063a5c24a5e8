import React from 'react';
import { NavLink } from 'react-router-dom';
import { cn } from '@/lib/utils';

const Navbar: React.FC = () => {
  return (
    <nav className="bg-white/90 backdrop-blur-md shadow-md border-b border-gray-100 sticky top-0 z-50 transition-all duration-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <i className="fa-solid fa-heartbeat text-blue-600 text-2xl mr-2"></i>
              <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">疾病风险预测系统</span>
            </div>
            <div className="hidden sm:ml-10 sm:flex space-x-1">
              <NavLink
                to="/"
                className={({ isActive }) =>
                  cn(
                    "inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                    isActive 
                      ? "bg-blue-50 text-blue-700 shadow-sm" 
                      : "text-gray-700 hover:bg-gray-50"
                  )
                }
              >
                <i className="fa-solid fa-home mr-2"></i>首页
              </NavLink>
              <NavLink
                to="/rules"
                className={({ isActive }) =>
                  cn(
                    "inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                    isActive 
                      ? "bg-blue-50 text-blue-700 shadow-sm" 
                      : "text-gray-700 hover:bg-gray-50"
                  )
                }
              >
  <i className="fa-solid fa-list-alt mr-2"></i>风险预测规则
              </NavLink>
              <NavLink
                to="/patients"
                className={({ isActive }) =>
                  cn(
                    "inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                    isActive 
                      ? "bg-blue-50 text-blue-700 shadow-sm" 
                      : "text-gray-700 hover:bg-gray-50"
                  )
                }
              >
                <i className="fa-solid fa-user-md mr-2"></i>患者风险标记
              </NavLink>
            </div>
          </div>
          <div className="flex items-center">
            <div className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium flex items-center">
              <i className="fa-solid fa-shield mr-1.5"></i>法布雷病专项版
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;