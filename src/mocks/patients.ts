import { Patient, ScoringItem } from '@/types';
import { diseaseRules } from './diseaseRules';

// 生成随机风险得分
const getRandomRiskScore = (): number => {
  return Math.floor(Math.random() * 100) + 1;
};

// 生成患者的评分项目
const generateScoringItems = (patientId: string): ScoringItem[] => {
  // 获取活跃的规则
  const activeRules = diseaseRules.filter(rule => rule.isActive);
  
  // 为每个规则生成评分项
  return activeRules.map((rule, index) => {
    // 随机决定是否匹配该规则
    const isMatched = Math.random() > 0.3;
    
    // 根据规则权重和匹配状态生成得分
    const maxScore = rule.weight;
    const score = isMatched ? maxScore * (0.8 + Math.random() * 0.2) : Math.random() * maxScore * 0.3;
    
    // 生成描述
    const descriptions = [
      "患者表现出与该规则高度吻合的特征",
      "部分符合该规则描述的临床表现",
      "检查结果显示与该规则有一定关联",
      "存在该规则提及的部分指标异常",
      "临床症状与该规则描述有相似之处"
    ];
    
  // 生成匹配详情和来源数据
  const matchDetails = [
    "患者主诉存在明显的肢端疼痛症状",
    "体格检查发现皮肤血管角质瘤",
    "超声心动图显示乳头肌肥大",
    "心脏磁共振检查显示Native T1值降低",
    "实验室检查发现蛋白尿和血尿",
    "患者有血液透析史，开始透析年龄为38岁",
    "超声心动图显示左心室肥厚"
  ];
  
  const referenceValues = [
    "正常范围: 1200-1400 ms",
    "正常范围: <3.6 cm²",
    "正常范围: 0.12-0.2 s",
    "正常范围: <130/80 mmHg",
    "正常范围: 0-0.3 g/24h"
  ];
  
  const sourceDataExamples = [
    "电子病历: 患者主诉'双下肢麻木疼痛3月余'",
    "超声报告: 乳头肌面积3.8 cm²，左心室横截面积比0.21",
    "MRI报告: Native T1值1180 ms",
    "检验报告: 尿蛋白定量0.8 g/24h",
    "透析记录: 首次透析日期2023-05-15，年龄38岁"
  ];
  
  // 定义匹配详情和来源数据对
  const detailSourcePairs = [
    { detail: matchDetails[0], reference: referenceValues[0], source: sourceDataExamples[0] },
    { detail: matchDetails[1], reference: referenceValues[1], source: sourceDataExamples[1] },
    { detail: matchDetails[2], reference: referenceValues[2], source: sourceDataExamples[2] },
    { detail: matchDetails[3], reference: referenceValues[3], source: sourceDataExamples[3] },
    { detail: matchDetails[4], reference: referenceValues[4], source: sourceDataExamples[4] }
  ];
  
  // 随机选择一个匹配的详情-来源数据对
  const selectedPair = isMatched ? 
    detailSourcePairs[Math.floor(Math.random() * detailSourcePairs.length)] : 
    null;
  
  return {
    id: `SI${patientId}${index}`,
    ruleId: rule.id,
    ruleName: rule.ruleName,
    score: Math.round(score),
    maxScore,
    isMatched,
    description: descriptions[Math.floor(Math.random() * descriptions.length)],
    matchedDetails: selectedPair ? selectedPair.detail : "未匹配相关指标",
    referenceValue: selectedPair ? selectedPair.reference : "",
    sourceData: selectedPair ? selectedPair.source : ""
  };
  });
};

// 生成随机日期（过去3个月内）
const getRandomDate = (): string => {
  const now = new Date();
  const threeMonthsAgo = new Date(now.setMonth(now.getMonth() - 3));
  const randomTime = threeMonthsAgo.getTime() + Math.random() * (Date.now() - threeMonthsAgo.getTime());
  return new Date(randomTime).toISOString().split('T')[0];
};

export const patients: Patient[] = [
  {
    id: 'PT001',
    patientName: '张**',
    patientId: 'P20250001',
    gender: 'male',
    riskDisease: '法布雷病',
    riskScore: 85,
    scoringItems: generateScoringItems('PT001'),
    lastPredictionTime: getRandomDate()
  },
  {
    id: 'PT002',
    patientName: '李**',
    patientId: 'P20250002',
    gender: 'female',
    riskDisease: '法布雷病',
    riskScore: 62,
    scoringItems: generateScoringItems('PT002'),
    lastPredictionTime: getRandomDate()
  },
  {
    id: 'PT003',
    patientName: '王**',
    patientId: 'P20250003',
    gender: 'male',
    riskDisease: '法布雷病',
    riskScore: 28,
    scoringItems: generateScoringItems('PT003'),
    lastPredictionTime: getRandomDate()
  },
  {
    id: 'PT004',
    patientName: '赵**',
    patientId: 'P20250004',
    gender: 'female',
    riskDisease: '法布雷病',
    riskScore: 91,
    scoringItems: generateScoringItems('PT004'),
    lastPredictionTime: getRandomDate()
  },
  {
    id: 'PT005',
    patientName: '刘**',
    patientId: 'P20250005',
    gender: 'male',
    riskDisease: '法布雷病',
    riskScore: 45,
    scoringItems: generateScoringItems('PT005'),
    lastPredictionTime: getRandomDate()
  },
  {
    id: 'PT006',
    patientName: '陈**',
    patientId: 'P20250006',
    gender: 'female',
    riskDisease: '法布雷病',
    riskScore: 78,
    scoringItems: generateScoringItems('PT006'),
    lastPredictionTime: getRandomDate()
  },
  {
    id: 'PT007',
    patientName: '杨**',
    patientId: 'P20250007',
    gender: 'male',
    riskDisease: '法布雷病',
    riskScore: 33,
    scoringItems: generateScoringItems('PT007'),
    lastPredictionTime: getRandomDate()
  },
  {
    id: 'PT008',
    patientName: '黄**',
    patientId: 'P20250008',
    gender: 'female',
    riskDisease: '法布雷病',
    riskScore: 56,
    scoringItems: generateScoringItems('PT008'),
    lastPredictionTime: getRandomDate()
  }
]